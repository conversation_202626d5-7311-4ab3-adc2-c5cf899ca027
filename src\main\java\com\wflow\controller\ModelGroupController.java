package com.wflow.controller;

import com.wflow.service.ModelGroupService;
import com.wflow.utils.R;
import com.wflow.utils.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> willian fu
 * @date : 2020/9/17
 */
@RestController
@RequestMapping("wflow/model")
@Tag(name = "流程模型分组管理")
public class ModelGroupController {

    @Autowired
    private ModelGroupService modelGroupService;


    /**
     * 查询所有分组流程模型数据
     * @param modelName 流程名称
     * @param modelType 流程类型
     * @return 列表数据
     */
    @GetMapping("group/list")
    @Operation(summary = "查询所有分组流程模型数据")
    public Object getGroupModels(@RequestParam(required = false) String modelName,@RequestParam(required = false) String modelType) {
        return R.ok(modelGroupService.getGroupModels(null, modelName,modelType));
    }

    /**
     * 查询所有流程模型
     * @return 列表数据
     */
    @GetMapping("list")
    @Operation(summary = "查询所有流程模型")
    public Object getModelItem() {
        return R.ok(modelGroupService.getModelItem());
    }

    /**
     * 获取用户可见的流程列表
     * @param modelName 流程模型名筛选
     * @return 列表数据
     */
    @GetMapping("list/byUser")
    @Operation(summary = "获取用户可见的流程列表")
    public Object getUserModels(@RequestParam(required = false) String modelName,@RequestParam(required = false) String modelType) {
        return R.ok(modelGroupService.getGroupModels(UserUtil.getLoginUserId(),modelName,modelType));
    }

    /**
     * 移动流程到新分组
     * @param modelId 流程模型
     * @param groupId 分组
     */
    @PutMapping("{modelId}/move/{groupId}")
    @Operation(summary = "移动流程到新分组")
    public Object modelMoveToGroup(@PathVariable String modelId,
                                 @PathVariable Long groupId) {
        modelGroupService.modelMoveToGroup(modelId, groupId);
        return R.ok("移动到新分组成功");
    }

    /**
     * 查询所有模型分组
     *
     * @return
     */
    @GetMapping("group")
    @Operation(summary = "查询所有模型分组")
    public Object getModelGroups() {
        return R.ok(modelGroupService.getModelGroups());
    }

    /**
     * 表单分组排序
     *
     * @param groups 分组数据
     * @return 排序结果
     */
    @PutMapping("group/sort")
    @Operation(summary = "表单分组排序")
    public Object modelGroupsSort(@RequestBody List<Long> groups) {
        modelGroupService.modelGroupsSort(groups);
        return R.ok("分组排序成功");
    }

    /**
     * 表单排序
     *
     * @param groupId 需要进行重排序的分组
     * @param modelIds 分组内表单排序ID
     * @return 排序结果
     */
    @PutMapping("sort/{groupId}")
    @Operation(summary = "表单排序")
    public Object groupModelSort(@PathVariable Long groupId,
                                 @RequestBody List<String> modelIds) {
        modelGroupService.groupModelSort(groupId, modelIds);
        return R.ok("移动位置成功");
    }

    /**
     * 修改分组
     *
     * @param groupId   分组ID
     * @param name 分组名
     * @return 修改结果
     */
    @PutMapping("group/{groupId}")
    @Operation(summary = "修改分组")
    public Object updateModelGroupName(@PathVariable Long groupId,
                                       @RequestParam String name) {
        modelGroupService.updateModelGroupName(groupId, name);
        return R.ok("修改分组成功");
    }
    /**
     * 启用或停用流程模型
     * @param modelId 模型ID
     * @param type 是否启用
     * @return 启用或停用流程模型
     */
    @PutMapping("{modelId}/active/{type}")
    @Operation(summary = "启用或停用流程模型")
    public Object enOrDisModel(@PathVariable String modelId,
                               @PathVariable Boolean type) {
        modelGroupService.enOrDisModel(modelId, type);
        return R.ok(Boolean.TRUE.equals(type) ? "停用流程成功":"启用流程成功");
    }

    /**
     * 新增表单分组
     *
     * @param name 分组名
     * @return 添加结果
     */
    @PostMapping("group")
    @Operation(summary = "新增表单分组")
    public Object createModelGroup(@RequestParam String name) {
        modelGroupService.createModelGroup(name);
        return R.ok("新增分组成功");
    }

    /**
     * 删除分组
     *
     * @param groupId 分组ID
     * @return 删除结果
     */
    @DeleteMapping("group/{groupId}")
    @Operation(summary = "删除分组")
    public Object deleteModelGroup(@PathVariable Long groupId) {
        modelGroupService.deleteModelGroup(groupId);
        return R.ok("删除分组成功");
    }

    /**
     * 删除流程模型
     * @param modelId id
     * @return 删除结果
     */
    @DeleteMapping("{modelId}")
    @Operation(summary = "删除流程模型")
    public Object deleteModel(@PathVariable String modelId) {
        modelGroupService.deleteModel(modelId);
        return R.ok("删除流程成功");
    }

    /**
     * 查询流程模型数据
     *
     * @param formId 模板id
     * @return 流程模型详情数据
     */
    @GetMapping("detail/{formId}")
    @Operation(summary = "查询流程模型数据")
    public Object getModelById(@PathVariable String formId) {
        return R.ok(modelGroupService.getModelById(formId));
    }

    /**
     * 通过流程定义ID查询流程
     * @param defId 流程部署后生成的定义ID
     * @return 流程
     */
    @GetMapping("detail/def/{defId}")
    @Operation(summary = "通过流程定义ID查询流程")
    public Object getModelByDefId(@PathVariable String defId) {
        return R.ok(modelGroupService.getModelByDefId(defId));
    }

    /**
     * 查询流程模型数据
     *
     * @param formId 模板id
     * @return 流程模型详情数据
     */
    @GetMapping("/processModel/{formId}")
    @Operation(summary = "查询流程模型数据")
    public Object getNodeModelById(@PathVariable String formId) {
        return R.ok(modelGroupService.getNodeModelById(formId));
    }
    /**
     * 预览获取表单数据
     * @param formId 流程ID
     * @param dirId 目录ID
     * @param version 版本号
     * @param nodeId 节点id
     * @return 表单字段
     */
    @Operation(summary = "预览获取表单数据")

    @GetMapping("/formData")
    public Object getFormItem(@RequestParam String formId,
                              @RequestParam String dirId,
                              @RequestParam(required = false) Integer version,
                              @RequestParam(required = false) String nodeId) {
        return R.ok(modelGroupService.getFormItem(formId,dirId,version,nodeId));
    }

}
