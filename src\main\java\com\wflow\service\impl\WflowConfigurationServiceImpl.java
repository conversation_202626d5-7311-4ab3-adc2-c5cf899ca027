package com.wflow.service.impl;

import com.wflow.bean.entity.WflowConfiguration;
import com.wflow.mapper.WflowConfigurationfMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class WflowConfigurationServiceImpl {
    @Resource
    private WflowConfigurationfMapper wflowConfigurationfMapper;

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(WflowConfiguration configuration) {
        wflowConfigurationfMapper.insertOrUpdate(configuration);
    }
}
