package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
*@Author：qinyi
*@Date：2024/10/28  10:31
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "sys_user")
public class User implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 姓名
     */
    private String nickName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 注册人
     */
    private String registerUser;

    /**
     * 机构id
     */
    private Long institutionId;

    private String sign;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 显示顺序
     */
    private Integer showSort;

    /**
     * 注册时间
     */
    private String registerTime;

    /**
     * 激活状态0待提交1未激活2审核失败3激活
     */
    private Integer activeStatus;

    /**
     * 角色ids逗号分隔
     */
    private String roleIds;

    /**
     * 访问权限ids逗号分隔
     */
    private String accessPermissionsIds;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 最后操作人
     */
    private String operater;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 办公电话
     */
    private String officePhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 家庭电话
     */
    private String familyPhone;

    private Integer isLock;

    @TableField(exist = false)
    private String institution;

    @TableField(exist = false)
    private String deptName;

    /**
     * 密码最后更新时间
     */
    private String pwdUpdateDate;


    /**
     * 头像
     */
    private String avatar;

    /**
     * sex
     */
    private String sex;

    /**
     * age
     */
    private String age;


    /**
     * birthday
     */
    private String birthday;

    private static final long serialVersionUID = 1L;

}