package com.wflow.workflow.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsHomeVo {
    @Schema(description = "报件总数")
    private Long total;
    @Schema(description = "信息公示")
    private Long message;
    @Schema(description = "征地实施环节")
    private Long link;
    @Schema(description = "已完成征地")
    private Long finished;
    @Schema(description = "未完成征地")
    private Long unfinished;
//    @Schema(description = "表单内容统计")
//    private List<FormInsideVo> formInsideVos;
}
