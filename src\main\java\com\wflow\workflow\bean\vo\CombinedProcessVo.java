package com.wflow.workflow.bean.vo;

import com.wflow.workflow.bean.process.OrgUser;
import com.wflow.workflow.bean.process.enums.ProcessResultEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 综合流程数据VO - 包含待办、已办、发起的、抄送的流程
 * <AUTHOR> willian fu
 * @date : 2025/01/09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "综合流程数据VO")
public class CombinedProcessVo {

    /**
     * 流程类型枚举
     */
    public enum ProcessType {
        TODO("待办"),
        DONE("已办"), 
        SUBMITTED("发起的"),
        CC("抄送");
        
        private final String desc;
        
        ProcessType(String desc) {
            this.desc = desc;
        }
        
        public String getDesc() {
            return desc;
        }
    }

    @Schema(description = "流程类型")
    private ProcessType processType;
    
    @Schema(description = "任务ID（待办和已办时有值）")
    private String taskId;
    
    @Schema(description = "任务定义key")
    private String taskDefKey;
    
    @Schema(description = "任务名称")
    private String taskName;
    
    @Schema(description = "任务处理结果（已办时有值）")
    private String taskResult;
    
    @Schema(description = "流程实例ID")
    private String instanceId;
    
    @Schema(description = "流程定义ID")
    private String processDefId;
    
    @Schema(description = "流程定义名称")
    private String processDefName;
    
    @Schema(description = "流程实例名称")
    private String instanceName;
    
    @Schema(description = "流程节点ID")
    private String nodeId;
    
    @Schema(description = "流程状态")
    private String status;
    
    @Schema(description = "流程结果")
    private ProcessResultEnum result;
    
    @Schema(description = "发起人用户ID")
    private String staterUserId;
    
    @Schema(description = "发起人信息")
    private OrgUser staterUser;
    
    @Schema(description = "表单数据摘要信息")
    private List<FormAbstractsVo> formAbstracts;
    
    @Schema(description = "流程开始时间")
    private Date startTime;
    
    @Schema(description = "流程结束时间")
    private Date finishTime;
    
    @Schema(description = "任务创建时间")
    private Date taskCreateTime;
    
    @Schema(description = "任务结束时间")
    private Date taskEndTime;
    
    @Schema(description = "部署ID")
    private String deployId;
    
    @Schema(description = "版本")
    private Integer version;
    
    @Schema(description = "表单ID")
    private String formId;
    
    @Schema(description = "父流程实例ID")
    private String superInstanceId;
    
    @Schema(description = "流程分组ID")
    private Long groupId;
    
    @Schema(description = "流程分组名称")
    private String groupName;
}
