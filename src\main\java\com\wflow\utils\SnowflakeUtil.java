package com.wflow.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

/**
 * 雪花ID工具类
 */
public class SnowflakeUtil {

    private static final long WORKER_ID = 0;

    private static final long DATA_CENTER_ID = 1;

    private static final Snowflake SNOWFLAKE = IdUtil.createSnowflake(WORKER_ID, DATA_CENTER_ID);

    public static String snowflakeId() {
        return SNOWFLAKE.nextIdStr();
    }

    public static void main(String[] args) {
        // System.out.println(snowflakeId());
        // System.out.println(DigestUtil.md5Hex("123"));

        for (int i = 0; i < 10; i++) {
            System.out.println(snowflakeId());
        }
    }

}
