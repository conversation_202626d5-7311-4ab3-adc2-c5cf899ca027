package com.wflow.workflow.listener;

import com.wflow.workflow.bean.jumpback.JumpBackConfig;
import com.wflow.workflow.bean.jumpback.JumpBackResult;
import com.wflow.workflow.service.JumpBackConfigService;
import com.wflow.workflow.service.JumpBackExecutionService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.engine.delegate.event.FlowableProcessEngineEvent;
import org.flowable.engine.delegate.event.impl.FlowableProcessEventImpl;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 流程回跳事件监听器
 * 监听流程和任务事件，触发回跳逻辑
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@Slf4j
public class JumpBackEventListener implements FlowableEventListener {

    private JumpBackConfigService jumpBackConfigService;
    private JumpBackExecutionService jumpBackExecutionService;

    public JumpBackEventListener(JumpBackConfigService jumpBackConfigService,
            JumpBackExecutionService jumpBackExecutionService) {
        this.jumpBackConfigService = jumpBackConfigService;
        this.jumpBackExecutionService = jumpBackExecutionService;
    }

    @Override
    public void onEvent(FlowableEvent event) {
        try {
            String processInstanceId = getProcessInstanceId(event);
            log.debug("接收到流程事件: {}, 类型: {}", processInstanceId, event.getType());

            if ("PROCESS_COMPLETED".equals(event.getType().name())) {
                handleProcessCompleted(event);
            } else if ("TASK_COMPLETED".equals(event.getType().name())) {
                handleTaskCompleted(event);
            } else if ("ACTIVITY_COMPLETED".equals(event.getType().name())) {
                handleActivityCompleted(event);
            }
        } catch (Exception e) {
            log.error("处理流程回跳事件时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理流程完成事件
     * 检查是否有需要触发的回跳操作
     * 
     * @param event 流程事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleProcessCompleted(FlowableEvent event) {
        String processInstanceId = getProcessInstanceId(event);
        log.info("处理流程完成事件: {}", processInstanceId);

        try {
            if (processInstanceId == null) {
                log.warn("流程实例ID为空，跳过回跳检查");
                return;
            }

            // 获取流程定义ID
            String processDefinitionId = getProcessDefinitionId(event);
            if (processDefinitionId == null) {
                log.warn("无法获取流程定义ID，跳过回跳检查");
                return;
            }

            // 获取该流程定义的所有回跳配置
            List<JumpBackConfig> configs = jumpBackConfigService.getJumpBackConfigsByProcessDef(processDefinitionId);
            if (configs.isEmpty()) {
                log.debug("流程定义 {} 没有配置回跳规则", processDefinitionId);
                return;
            }

            // 检查每个回跳配置
            for (JumpBackConfig config : configs) {
                if (!config.getEnabled()) {
                    log.debug("回跳配置 {} 已禁用，跳过", config.getId());
                    continue;
                }

                // 检查回跳条件
                if (checkJumpBackConditionForProcessCompleted(processInstanceId, config)) {
                    log.info("流程 {} 满足回跳条件，配置ID: {}", processInstanceId, config.getId());

                    // 异步执行回跳操作，避免阻塞当前事务
                    executeJumpBackAsync(processInstanceId, config.getId(), "流程完成触发回跳");
                }
            }
        } catch (Exception e) {
            log.error("处理流程完成事件时发生异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理任务完成事件
     * 检查任务完成是否触发回跳条件
     * 
     * @param event 任务事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleTaskCompleted(FlowableEvent event) {
        String processInstanceId = getProcessInstanceId(event);
        log.debug("处理任务完成事件: {}", processInstanceId);

        try {
            if (processInstanceId == null) {
                return;
            }

            // 获取流程定义ID
            String processDefinitionId = getProcessDefinitionId(event);
            if (processDefinitionId == null) {
                return;
            }

            // 获取该流程定义的所有回跳配置
            List<JumpBackConfig> configs = jumpBackConfigService.getJumpBackConfigsByProcessDef(processDefinitionId);

            for (JumpBackConfig config : configs) {
                if (!config.getEnabled()) {
                    continue;
                }

                // 检查是否为特定分支完成的回跳条件
                if (config.getJumpCondition() == JumpBackConfig.JumpBackCondition.SPECIFIC_BRANCH_COMPLETED ||
                        config.getJumpCondition() == JumpBackConfig.JumpBackCondition.ANY_BRANCH_COMPLETED) {

                    if (jumpBackExecutionService.checkJumpBackCondition(processInstanceId, config.getSourceNodeId())) {
                        log.info("任务完成触发回跳条件，流程: {}, 配置: {}", processInstanceId, config.getId());
                        executeJumpBackAsync(processInstanceId, config.getId(), "任务完成触发回跳");
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理任务完成事件时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理活动完成事件
     * 主要用于网关节点的完成检测
     * 
     * @param event 活动事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleActivityCompleted(FlowableEvent event) {
        String processInstanceId = getProcessInstanceId(event);
        log.debug("处理活动完成事件: {}", processInstanceId);

        try {
            if (!(event instanceof FlowableProcessEngineEvent)) {
                return;
            }

            FlowableProcessEngineEvent processEvent = (FlowableProcessEngineEvent) event;
            String activityId = getActivityId(processEvent);

            if (activityId == null || processInstanceId == null) {
                return;
            }

            // 获取流程定义ID
            String processDefinitionId = getProcessDefinitionId(event);
            if (processDefinitionId == null) {
                return;
            }

            // 检查是否有针对该活动的回跳配置
            JumpBackConfig config = jumpBackConfigService.getJumpBackConfig(processDefinitionId, activityId);
            if (config != null && config.getEnabled()) {

                // 检查回跳条件
                if (jumpBackExecutionService.checkJumpBackCondition(processInstanceId, activityId)) {
                    log.info("活动完成触发回跳条件，流程: {}, 活动: {}, 配置: {}",
                            processInstanceId, activityId, config.getId());
                    executeJumpBackAsync(processInstanceId, config.getId(), "活动完成触发回跳");
                }
            }
        } catch (Exception e) {
            log.error("处理活动完成事件时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查流程完成时的回跳条件
     * 
     * @param processInstanceId 流程实例ID
     * @param config            回跳配置
     * @return 是否满足回跳条件
     */
    private boolean checkJumpBackConditionForProcessCompleted(String processInstanceId, JumpBackConfig config) {
        try {
            // 检查回跳次数限制
            int currentJumpCount = jumpBackExecutionService.getCurrentJumpCount(processInstanceId, config.getId());
            if (currentJumpCount >= config.getMaxJumpCount()) {
                log.info("流程 {} 已达到最大回跳次数限制: {}", processInstanceId, config.getMaxJumpCount());
                return false;
            }

            // 检查是否可以回跳
            if (!jumpBackExecutionService.canJumpBack(processInstanceId, config.getId())) {
                log.debug("流程 {} 当前不满足回跳条件", processInstanceId);
                return false;
            }

            // 根据回跳条件类型进行具体检查
            switch (config.getJumpCondition()) {
                case ALL_BRANCHES_COMPLETED:
                    return checkAllBranchesCompleted(processInstanceId, config);
                case ANY_BRANCH_COMPLETED:
                    return checkAnyBranchCompleted(processInstanceId, config);
                case MANUAL_TRIGGER:
                    // 手动触发不在此处处理
                    return false;
                default:
                    return jumpBackExecutionService.checkJumpBackCondition(processInstanceId, config.getSourceNodeId());
            }
        } catch (Exception e) {
            log.error("检查回跳条件时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查所有分支是否完成
     */
    private boolean checkAllBranchesCompleted(String processInstanceId, JumpBackConfig config) {
        // 这里需要根据具体的业务逻辑实现
        // 可以通过查询流程历史、检查分支状态等方式判断
        return jumpBackExecutionService.checkJumpBackCondition(processInstanceId, config.getSourceNodeId());
    }

    /**
     * 检查任意分支是否完成
     */
    private boolean checkAnyBranchCompleted(String processInstanceId, JumpBackConfig config) {
        // 这里需要根据具体的业务逻辑实现
        return jumpBackExecutionService.checkJumpBackCondition(processInstanceId, config.getSourceNodeId());
    }

    /**
     * 异步执行回跳操作
     * 
     * @param processInstanceId 流程实例ID
     * @param configId          配置ID
     * @param reason            回跳原因
     */
    private void executeJumpBackAsync(String processInstanceId, String configId, String reason) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始异步执行回跳操作: 流程={}, 配置={}, 原因={}", processInstanceId, configId, reason);

                JumpBackResult result = jumpBackExecutionService.executeJumpBack(processInstanceId, configId, reason);

                if (result.getSuccess()) {
                    log.info("回跳操作执行成功: {}", result);
                } else {
                    log.error("回跳操作执行失败: {}", result.getErrorMessage());
                }
            } catch (Exception e) {
                log.error("异步执行回跳操作时发生异常: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 获取流程实例ID
     */
    private String getProcessInstanceId(FlowableEvent event) {
        try {
            if (event instanceof FlowableProcessEngineEvent) {
                FlowableProcessEngineEvent processEvent = (FlowableProcessEngineEvent) event;
                return processEvent.getProcessInstanceId();
            }
            return null;
        } catch (Exception e) {
            log.warn("获取流程实例ID时发生异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取活动ID
     */
    private String getActivityId(FlowableProcessEngineEvent event) {
        try {
            if (event instanceof FlowableProcessEventImpl) {
                FlowableProcessEventImpl processEvent = (FlowableProcessEventImpl) event;
                ExecutionEntity execution = (ExecutionEntity) processEvent.getExecution();
                return execution.getCurrentActivityId();
            }
            return null;
        } catch (Exception e) {
            log.warn("获取活动ID时发生异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取流程定义ID
     */
    private String getProcessDefinitionId(FlowableEvent event) {
        try {
            if (event instanceof FlowableProcessEventImpl) {
                FlowableProcessEventImpl processEvent = (FlowableProcessEventImpl) event;
                ExecutionEntity execution = (ExecutionEntity) processEvent.getExecution();
                return execution.getProcessDefinitionId();
            }
            return null;
        } catch (Exception e) {
            log.warn("获取流程定义ID时发生异常: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public boolean isFailOnException() {
        // 返回false，避免回跳异常影响主流程
        return false;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        // 在事务提交后执行，确保数据一致性
        return true;
    }

    @Override
    public String getOnTransaction() {
        // 在事务提交后执行
        return "after-commit";
    }
}
