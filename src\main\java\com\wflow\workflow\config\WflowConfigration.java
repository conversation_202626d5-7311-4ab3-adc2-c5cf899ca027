package com.wflow.workflow.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> willian fu
 * @date : 2024/2/25
 */
@ConfigurationProperties(prefix = "wflow")
public class WflowConfigration {
    @Data
    public static class file {
        /**
         * 文件上传路径
         */
        private String upload;
        /**
         * 日志存储路径
         */
        private String log;
    }
}
