# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-05-07 10:38:15 - Log of updates made will be appended as footnotes to the end of this file.

## Project Goal

* 构建基于Spring Boot的工作流管理系统
* 集成Flowable工作流引擎
* 提供表单设计和工作流配置能力

## Key Features

* 表单管理与设计
* 工作流模型定义与执行  
* 组织架构集成
* 权限控制与审批流程

## Overall Architecture

* 分层架构：Controller-Service-DAO
* 核心模块：工作流引擎、表单管理、组织架构
* 数据库：MySQL/Oracle/PostGreSQL支持