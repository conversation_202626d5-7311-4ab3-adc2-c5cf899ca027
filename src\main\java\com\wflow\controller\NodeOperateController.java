package com.wflow.controller;

import com.wflow.bean.entity.WflowFormDir;
import com.wflow.bean.entity.WflowFormInfo;
import com.wflow.bean.entity.WflowNodeOperate;
import com.wflow.service.FormInfoManageService;
import com.wflow.service.ModelHistorysFormIdService;
import com.wflow.service.WflowNodeOperateService;
import com.wflow.utils.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> qinyi
 * @date : 2024/12/19
 */
@RestController
@RequestMapping("wflow/nodeOperate")
@Tag(name = "环节操作管理")
public class NodeOperateController {

    @Autowired
    private WflowNodeOperateService nodeOperateService; ;



    @PostMapping("/add")
    @Operation(summary = "新增操作")
    public Object createNodeOperate(@RequestBody WflowNodeOperate wflowNodeOperate) {
        return nodeOperateService.createNodeOperate(wflowNodeOperate);
    }

    @PostMapping("/edit")
    @Operation(summary = "编辑操作")
    public Object editNodeOperate(@RequestBody WflowNodeOperate wflowNodeOperate) {
        return nodeOperateService.editNodeOperate(wflowNodeOperate);
    }

    @PostMapping("/del")
    @Operation(summary = "删除/批量删除操作")
    public Object delNodeOperate(@RequestParam String ids) {
        return nodeOperateService.delNodeOperate(ids);
    }

    @PostMapping("/delNode")
    @Operation(summary = "删除/批量删除节点操作")
    public Object delNode(@RequestParam String nodeId,@RequestParam String operateIds) {
        return nodeOperateService.delNodeOperate2(nodeId,operateIds);
    }

    @GetMapping("/innerList")
    @Operation(summary = "查询操作列表/环节操作列表(带条件搜索)")
    public Object innerList(@RequestParam(required = false) String nodeId,@RequestParam(required = false) String operateName,@RequestParam(required = false) String operateSign){
        return nodeOperateService.innerList(nodeId,operateName,operateSign);
    }

//    @GetMapping("/saveToOutList")
//    @Operation(summary = "保存到外层列表")
//    public Object saveToOutList(@RequestParam String nodeOperateIds){
//        return nodeOperateService.saveToOutList(nodeOperateIds);
//    }

//    @GetMapping("/getNodeOperateList")
//    @Operation(summary = "查询环节操作列表(外层)带条件搜索")
//    public Object getNodeOperateList(@RequestParam String nodeId,@RequestParam String operateName,@RequestParam String operateSign){
//        return nodeOperateService.getNodeOperateList(nodeId,operateName,operateSign);
//    }

//    @PostMapping("/delNodeOperate")
//    @Operation(summary = "删除环节操作(外层)")
//    public Object delOutNodeOperate(@RequestParam String ids) {
//        return nodeOperateService.delOutNodeOperate(ids);
//    }
    @PostMapping("/copyFrom")
    @Operation(summary = "复制从")
    public Object copyFrom(@RequestParam String nodeId,@RequestParam String fromNodeId,@RequestParam Long modelHistorysId){
        return nodeOperateService.copyFrom(nodeId,fromNodeId,modelHistorysId);
    }

    @PostMapping("/relationOperaToNode")
    @Operation(summary = "关联操作到节点")
    public Object relationOperaToNode(@RequestParam String nodeId,@RequestParam String operateIds){
        return nodeOperateService.relationOperaToNode(nodeId,operateIds);
    }

}
