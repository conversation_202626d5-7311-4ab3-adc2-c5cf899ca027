package com.wflow.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public class JsonStreamMerger {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 使用Stream合并JSON数组字段
     * @param objects 对象列表
     * @param fieldExtractor 字段提取函数（如 MyObject::getJsonArr）
     * @return 合并后的JSON数组字符串
     */
    public static <T> String mergeWithStream(List<T> objects, Function<T, String> fieldExtractor) {
        List<Object> merged = objects.stream()
                .map(fieldExtractor)
                .filter(str -> str != null && (!str.isEmpty()))
                .flatMap(JsonStreamMerger::parseJson)
                .collect(Collectors.toList());

        return serialize(merged);
    }

    // 解析JSON数组并展开为流
    private static Stream<Object> parseJson(String jsonStr) {
        try {
            List<?> elements = OBJECT_MAPPER.readValue(jsonStr, List.class);
            return (Stream<Object>) elements.stream();
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("无效JSON数组: " + jsonStr, e);
        }
    }

    // 序列化最终结果
    private static String serialize(List<Object> data) {
        try {
            return OBJECT_MAPPER.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON生成失败", e);
        }
    }
}
