package com.wflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wflow.bean.entity.User;
import com.wflow.bean.entity.WflowUsers;
import com.wflow.bean.vo.OrgTreeVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> willian fu
 * @date : 2022/6/27
 */
public interface WflowUsersMapper extends BaseMapper<User> {

    /**
     * 查询该部门下的所有用户
     * @param deptId 部门ID
     * @return 用户列表 type为固定值user
     */
    @Select("SELECT id, nick_name as name, avatar " +
            "FROM sys_user " +
            "WHERE dept_id = #{deptId} and is_lock=0")
    List<OrgTreeVo> selectUsersByDept(@Param("deptId") Long deptId);

    /**
     * 通过拼音搜索用户，全拼和拼音首字母模糊搜索
     * @param py 拼音
     * @return 搜索的用户列表 type为固定值user
     */
    @Select("SELECT ID\n" +
            "  ,\n" +
            "name ,\n" +
            "type\n" +
            "FROM\n" +
            "  (\n" +
            "  SELECT ID\n" +
            "    ,\n" +
            "    nick_name AS NAME ,\n" +
            "    'user' AS type\n" +
            "  FROM\n" +
            "    sys_user \n" +
            "  WHERE\n" +
            "    ( nick_name LIKE'%${py}%' OR username LIKE'%${py}%' ) \n" +
            "    AND is_lock = 0 UNION all\n" +
            "    SELECT ID\n" +
            "    ,\n" +
            "    dept_name AS NAME ,\n" +
            "    'dept' AS type\n" +
            "  FROM\n" +
            "    sys_dept \n" +
            "  WHERE\n" +
            "  dept_name LIKE'%${py}%' union ALL\n" +
            "  SELECT ID,\n" +
            "  institution_name AS name,\n" +
            "  'institution' AS type\n" +
            "  FROM\n" +
            "  sys_institution\n" +
            "  WHERE\n" +
            "  institution_name like '%${py}%'\n" +
            "  ) AS filtered_users\n" +
            "  ORDER BY name")
    List<OrgTreeVo> selectUsersByPy(@Param("py") String py);
}
