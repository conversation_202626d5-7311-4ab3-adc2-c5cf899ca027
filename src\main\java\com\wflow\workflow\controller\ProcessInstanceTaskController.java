package com.wflow.workflow.controller;

import com.wflow.bean.dto.TodoRequestDto;
import com.wflow.utils.R;
import com.wflow.utils.UserUtil;
import com.wflow.workflow.bean.vo.ProcessHandlerParamsVo;
import com.wflow.workflow.service.ProcessTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * <AUTHOR> willian fu
 * @date : 2022/8/25
 */
@RestController
@RequestMapping("wflow/process/task")
@Tag(name = "用户流程操作管理")
public class ProcessInstanceTaskController {

    @Autowired
    private ProcessTaskService taskService;

    /**
     * 查询用户待办待处理的任务
     * @return 分页列表数据
     */
    @PostMapping("todoList")
    @Operation(summary ="查询用户待办待处理的任务")
    public Object getUserTodoList(@RequestBody TodoRequestDto todoRequestDto) {
        Integer pageSize = todoRequestDto.getPageSize();
        Integer pageNo = todoRequestDto.getPageNo();
        String code = todoRequestDto.getCode();
        String[] startTimes = todoRequestDto.getStartTimes();
        String keyword = todoRequestDto.getKeyword();
        String fieldId = todoRequestDto.getProjectId();
        Map<String, Object> form = todoRequestDto.getForm();
        return R.ok(taskService.getUserTodoList(pageSize, pageNo, code, startTimes, keyword,form,fieldId));
    }

    /**
     * 查询用户已审批的流程实例
     * @return 分页列表数据
     */
    @PostMapping("idoList")
    @Operation(summary ="查询用户已审批的流程实例")
    public Object getUserIdoList(@RequestBody TodoRequestDto todoRequestDto) {
        Integer pageSize = todoRequestDto.getPageSize();
        Integer pageNo = todoRequestDto.getPageNo();
        String code = todoRequestDto.getCode();
        Map<String, Object> form = todoRequestDto.getForm();
        String type = todoRequestDto.getType();
        return R.ok(taskService.getUserIdoList(pageSize, pageNo, code,type,form));
    }

    /**
     * 用户处理任务，审批、转交、评论、撤销等操作
     *
     * @param params 操作参数
     * @return 操作结果
     */
    @PostMapping("handler")
    @Operation(summary ="用户处理任务，审批、转交、评论、撤销等操作")
    public Object approvalTask(@RequestBody ProcessHandlerParamsVo params) {
        String userId = UserUtil.getLoginUserId();
        taskService.approvalTask(userId, userId, params);
        return R.ok("处理成功");
    }

    /**
     * 管理员处理任务接口
     *
     * @param owner 该任务原主人ID
     * @param params 操作参数
     * @return 操作结果
     */
    @PostMapping("replace/{owner}/handler")
    @Operation(summary ="管理员处理任务接口")
    public Object approvalTask(@PathVariable String owner, @RequestBody ProcessHandlerParamsVo params) {
        String userId = UserUtil.getLoginUserId();
        taskService.approvalTask(owner, userId, params);
        return R.ok("处理成功");
    }

    /**
     * 获取所有可回退的审批任务节点
     *
     * @param instanceId 审批实例ID
     * @param taskId     当前任务ID
     * @return 所有可回退节点
     */
    @GetMapping("recall/nodes")
    @Operation(summary ="获取所有可回退的审批任务节点")
    public Object getRecallTaskNodes(@RequestParam String instanceId,
                                     @RequestParam String taskId) {
        return R.ok(taskService.getRecallTaskNodes(instanceId, taskId));
    }

    /**
     * 查询节点审批设置项
     *
     * @param taskId 要处理的任务ID
     * @return 该任务所属节点的设置项
     */
    @GetMapping("settings/{taskId}")
    @Operation(summary ="查询节点审批设置项")
    public Object getTaskSettings(@PathVariable String taskId) {
        return R.ok(taskService.getNodeTaskSettings(taskId));
    }

    /**
     * 工作交接设置接口
     *
     * @param userId 交接人
     * @return 交接结果
     */
    @GetMapping("handover/{userId}")
    @Operation(summary ="工作交接设置接口")
    public Object workHandover(@PathVariable String userId) {
        taskService.workHandover(UserUtil.getLoginUserId(), userId);
        return R.ok("设置工作交接完毕");
    }



}
