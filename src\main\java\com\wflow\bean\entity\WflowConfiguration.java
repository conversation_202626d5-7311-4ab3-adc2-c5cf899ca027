package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;



@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "流程扩展配置")
public class WflowConfiguration {

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description ="主键")
    private Long id;
    /**
    * 流程模型Id
    */
    @Schema(description ="流程模型Id")
    private String modelId;
    /**
    * 流程动态表单虚拟目录json
    */
    @Schema(description = "流程动态表单虚拟目录json")
    private String dirData;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime created;

    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updated;

}