package com.wflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wflow.bean.entity.AsyncTask;
import com.wflow.bean.enums.TaskStatus;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 异步任务Mapper
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
public interface AsyncTaskMapper extends BaseMapper<AsyncTask> {

    /**
     * 根据状态查询任务列表
     */
    @Select("SELECT * FROM async_task WHERE status = #{status} ORDER BY priority DESC, created_time ASC")
    List<AsyncTask> selectByStatus(@Param("status") TaskStatus status);

    /**
     * 查询待处理的任务（按优先级和创建时间排序）
     */
    @Select("SELECT * FROM async_task WHERE status IN ('PENDING', 'PROCESSING') " +
            "ORDER BY priority DESC, created_time ASC LIMIT #{limit}")
    List<AsyncTask> selectPendingTasks(@Param("limit") int limit);

    /**
     * 更新任务状态
     */
    @Update("UPDATE async_task SET status = #{status}, progress = #{progress}, " +
            "start_time = #{startTime}, end_time = #{endTime}, error_message = #{errorMessage}, " +
            "actual_time = #{actualTime} WHERE task_id = #{taskId}")
    int updateTaskStatus(@Param("taskId") String taskId, 
                         @Param("status") TaskStatus status,
                         @Param("progress") Integer progress,
                         @Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime,
                         @Param("errorMessage") String errorMessage,
                         @Param("actualTime") Integer actualTime);

    /**
     * 更新任务进度
     */
    @Update("UPDATE async_task SET progress = #{progress} WHERE task_id = #{taskId}")
    int updateTaskProgress(@Param("taskId") String taskId, @Param("progress") Integer progress);

    /**
     * 更新任务结果
     */
    @Update("UPDATE async_task SET result = #{result}, status = #{status}, progress = 100, " +
            "end_time = #{endTime}, actual_time = #{actualTime} WHERE task_id = #{taskId}")
    int updateTaskResult(@Param("taskId") String taskId, 
                         @Param("result") String result,
                         @Param("status") TaskStatus status,
                         @Param("endTime") LocalDateTime endTime,
                         @Param("actualTime") Integer actualTime);

    /**
     * 增加重试次数
     */
    @Update("UPDATE async_task SET retry_count = retry_count + 1 WHERE task_id = #{taskId}")
    int incrementRetryCount(@Param("taskId") String taskId);

    /**
     * 查询过期的任务
     */
    @Select("SELECT * FROM async_task WHERE expire_time < #{currentTime} AND status NOT IN ('COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT')")
    List<AsyncTask> selectExpiredTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 删除过期的已完成任务
     */
    @Update("DELETE FROM async_task WHERE expire_time < #{expireTime} AND status IN ('COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT')")
    int deleteExpiredCompletedTasks(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 查询用户的任务列表
     */
    @Select("SELECT * FROM async_task WHERE created_by = #{userId} ORDER BY created_time DESC LIMIT #{limit}")
    List<AsyncTask> selectUserTasks(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 统计各状态任务数量
     */
    @Select("SELECT status, COUNT(*) as count FROM async_task GROUP BY status")
    List<Object> countTasksByStatus();
}
