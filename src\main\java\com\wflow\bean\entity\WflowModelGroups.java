package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WflowModelGroups implements Serializable {
    private static final long serialVersionUID = -40467384325438214L;
    /**
     * 分组ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long groupId;

    /**
    * 分组名称
    */
    @Schema(description ="子系统名称")
    private String groupName;
    /**
    * 排序
    */
    @Schema(description ="排序")
    private Integer sort;

    /**
    * 更新时间
    */
    @Schema(description ="更新时间")
    private Date updated;

    /**
     * 生成的子系统访问地址
     */
    @Schema(description ="生成的子系统访问地址")
    private String url;

}
