package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
*@Author：qinyi
*@Date：2024/10/28  15:46
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "sys_dept")
public class SafetyDept implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 显示顺序
     */
    private Integer showSort;

    /**
     * 所属机构id
     */
    private Long parentInstitutionId;

    /**
     * 上级部门id
     */
    private Long parentDeptId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 最后操作人
     */
    private String operater;

    private String leader;

    /**
     * 是否含有子部门
     */
    @TableField(exist = false)
    private Boolean isHasNode = false;

    /**
     * 上级部门名称
     */
    @TableField(exist = false)
    private String parentDeptName;


    private static final long serialVersionUID = 1L;
}