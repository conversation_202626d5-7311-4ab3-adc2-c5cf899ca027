package com.wflow.workflow.bean.process.props;

import com.alibaba.fastjson2.JSONArray;
import com.wflow.workflow.bean.process.OperationPerm;
import com.wflow.workflow.bean.process.OrgUser;
import com.wflow.workflow.bean.process.enums.ApprovalTypeEnum;
import com.wflow.workflow.bean.process.form.FormPerm;
import com.wflow.workflow.bean.process.form.NodeFormPerm;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> willian fu
 * @date : 2022/7/7
 */
@Data
public class RootProps implements Serializable {
    private static final long serialVersionUID = -45475579271153023L;

    private List<OrgUser> assignedUser;
    private List<FormPerm> formPerms;
    private OperationPerm operationPerm;
    private List<NodeFormPerm> materialsPerms;
    private Map<String, JSONArray> listeners;
    private ApprovalTypeEnum assignedType;
}
