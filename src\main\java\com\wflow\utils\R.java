package com.wflow.utils;

import cn.hutool.core.util.StrUtil;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR> willian fu
 * @version : 1.0
 */
public class R {
    public static <T> ResponseEntity<T> badRequest(T msg){
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(msg);
    }

    public static ResponseEntity<InputStreamResource> resource(InputStreamResource resource, String name) throws UnsupportedEncodingException {
        ResponseEntity.BodyBuilder builder = ResponseEntity.ok();
        if (StrUtil.isNotBlank(name)){
            String fileName = URLEncoder.encode(name, "utf-8");
            builder.header("Content-Disposition", "attachment; filename=" + fileName);
            builder.header("Access-Control-Expose-Headers", "Content-Disposition, download-filename");
            builder.header("download-filename", fileName);
        }
        builder.contentType(MediaType.APPLICATION_OCTET_STREAM);

        return builder.body(resource);
    }

    public static <T> ResponseEntity<T> notFound(T msg){
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(msg);
    }

    public static <T> ResponseEntity<T> serverError(T msg){
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(msg);
    }

    public static <T> ResponseEntity<T> unAuthorized(T msg){
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(msg);
    }

    public static <T> ResponseEntity<T> forbidden(T msg){
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(msg);
    }

    public static <T> ResponseEntity<T> noContent(T msg){
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(msg);
    }

    public static <T> ResponseEntity<T> ok(T msg){
        return ResponseEntity.ok(msg);
    }

    public static <T> ResponseEntity<T> created(T msg){
        return ResponseEntity.status(HttpStatus.CREATED).body(msg);
    }

    public static Object error(String msg) {
        return ResponseEntity.ok(msg);
    }

    /**
     * 返回成功响应，支持附加数据
     */
    public static SuccessResponse success(String message) {
        return new SuccessResponse(message);
    }

    /**
     * 成功响应构建器类
     */
    public static class SuccessResponse {
        private String message;
        private java.util.Map<String, Object> data = new java.util.HashMap<>();

        public SuccessResponse(String message) {
            this.message = message;
        }

        public SuccessResponse put(String key, Object value) {
            this.data.put(key, value);
            return this;
        }

        public String getMessage() {
            return message;
        }

        public java.util.Map<String, Object> getData() {
            return data;
        }
    }
}
