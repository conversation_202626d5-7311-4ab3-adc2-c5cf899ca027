package com.wflow.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.*;
import com.wflow.bean.vo.ModelGroupVo;
import com.wflow.bean.vo.WflowModelDetailVo;
import com.wflow.exception.BusinessException;
import com.wflow.mapper.*;
import com.wflow.service.FormInfoManageService;
import com.wflow.service.ModelGroupService;
import com.wflow.service.OrgRepositoryService;
import com.wflow.utils.R;
import com.wflow.workflow.WFlowToBpmnCreator;
import com.wflow.workflow.bean.process.Node;
import com.wflow.workflow.bean.process.OrgUser;
import com.wflow.workflow.bean.process.ProcessNode;
import com.wflow.workflow.bean.process.enums.ApprovalModeEnum;
import com.wflow.workflow.bean.process.enums.NodeTypeEnum;
import com.wflow.workflow.bean.process.form.Form;
import com.wflow.workflow.bean.process.props.ApprovalProps;
import com.wflow.workflow.bean.process.props.RootProps;
import com.wflow.workflow.bean.vo.ProcessConditionResolveParamsVo;
import com.wflow.workflow.bean.vo.ProcessProgressVo;
import com.wflow.workflow.service.FormService;
import com.wflow.workflow.service.ProcessModelService;
import com.wflow.workflow.service.ProcessNodeCatchService;
import liquibase.pro.packaged.W;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> qinyi
 * @date : 202411/21
 */
@Slf4j
@Service
@Transactional
public class FormInfoManageServiceImpl implements FormInfoManageService {

    @Autowired
    private WflowFormInfoMapper formInfoMapper;

    @Autowired
    private WflowFormDirMapper formDirMapper;

    @Autowired
    private WflowModelHistorysMapper modelHistorysMapper;

    @Autowired
    private WflowNodeFormInfoMapper nodeFormInfoMapper;

    @Autowired
    private WflowFormInfoHistorysMapper formInfoHistorysMapper;


    @Override
    public void createWflowFormDir(WflowFormDir dir) {
        dir.setCreateTime(LocalDateTime.now());

        formDirMapper.insert(dir);
    }

    @Override
    public void editWflowFormDir(WflowFormDir dir) {
        dir.setUpdateTime(LocalDateTime.now());
        formDirMapper.updateById(dir);
    }

    @Override
    public void delForminfoDir(String id) {
        formDirMapper.deleteById(id);
    }

    @Override
    public Object getForminfoDirTree() {
        List<WflowFormDir> dirs = formDirMapper.selectList(new LambdaQueryWrapper<WflowFormDir>().eq(WflowFormDir::getParentId, '0'));
        List<WflowFormDir> voList = new ArrayList<>();
        for (WflowFormDir dir : dirs) {
            WflowFormDir vo = BeanUtil.copyProperties(dir, WflowFormDir.class);
            List<WflowFormDir> childrens = findChildrenIterative(dir.getId());
            vo.setChildren(childrens);
            voList.add(vo);
        }
        return voList;
    }
    //改造增加历史表
    @Override
    public void createWflowForm(WflowFormInfo formInfo) {
        Long id = IdWorker.getId();
        formInfo.setCreated(LocalDateTime.now());
        formInfo.setVersion(1);
        formInfo.setId(String.valueOf(id));
        formInfoMapper.insert(formInfo);
        WflowFormInfoHistorys formInfoHistorys = new WflowFormInfoHistorys();
        BeanUtil.copyProperties(formInfo, formInfoHistorys);
        formInfoHistorysMapper.insert(formInfoHistorys);
    }

    @Override
    public void editWflowForm(WflowFormInfo formInfo) {
        Long id = IdWorker.getId();
        //查询该表单是否存在部署情况
        List<WflowNodeFormInfo> nodeFormInfo = nodeFormInfoMapper.selectList(
                Wrappers.<WflowNodeFormInfo>lambdaQuery()
                        .eq(WflowNodeFormInfo::getFormInfoId, String.valueOf(formInfo.getId())))
                .stream()
                .filter(Objects::nonNull)
                .filter(form->form.getProcessDefId() != null && !form.getProcessDefId().isEmpty())
                .collect(Collectors.toList());
        if(nodeFormInfo.isEmpty()){
            formInfo.setCreated(LocalDateTime.now());
            formInfoMapper.updateById(formInfo);
            WflowFormInfoHistorys formInfoHistorys = new WflowFormInfoHistorys();
            BeanUtil.copyProperties(formInfo, formInfoHistorys);
            formInfoHistorysMapper.update(formInfoHistorys, new LambdaQueryWrapper<WflowFormInfoHistorys>().eq(WflowFormInfoHistorys::getId, String.valueOf(formInfo.getId())));
        }else {
            formInfoMapper.deleteById(formInfo.getId());
            formInfo.setCreated(LocalDateTime.now());
            formInfo.setVersion(formInfo.getVersion()+1);
            formInfo.setId(String.valueOf(id));
            formInfoMapper.insert(formInfo);
            WflowFormInfoHistorys formInfoHistorys = new WflowFormInfoHistorys();
            BeanUtil.copyProperties(formInfo, formInfoHistorys);
            formInfoHistorysMapper.insert(formInfoHistorys);
        }
    }

    @Override
    public void delWflowForm(String id) {
        formInfoMapper.deleteById(id);
    }

    @Override
    public Object getForminfoList(String materialName, String formDirId,Integer page,Integer pageSize) {
        LambdaQueryWrapper<WflowFormInfo> wrapper = new LambdaQueryWrapper<WflowFormInfo>()
                .like(StringUtils.isNotBlank(materialName), WflowFormInfo::getMaterialName, materialName)
                .orderByDesc(WflowFormInfo::getVersion)
                .orderByAsc(WflowFormInfo::getSort);
        if(!Objects.isNull(formDirId)){
            List<String> id = formDirMapper.selectList(
                            Wrappers.<WflowFormDir>lambdaQuery()
                                    .select(WflowFormDir::getId)
                                    .eq(WflowFormDir::getParentId, formDirId)
                    )
                    .stream() // 转换为流
                    .map(WflowFormDir::getId)
                    .collect(Collectors.toList());
            id.add(formDirId);
            wrapper.in(WflowFormInfo::getFormDirId,id);
        }
        return formInfoMapper.selectPage(new Page<>(page, pageSize),wrapper);
    }

    @Override
    public Object getNodeList(String modelHistorysId) {
        WflowModelHistorys wflowModelHistorys = this.modelHistorysMapper.selectOne(
                new LambdaQueryWrapper<WflowModelHistorys>()
                        .eq(WflowModelHistorys::getId, Long.valueOf(modelHistorysId)));
        if(Objects.isNull(wflowModelHistorys)){
            return R.ok("未找到流程实例");
        }
        String process = wflowModelHistorys.getProcess();
        ProcessNode node = JSONObject.parseObject(process, ProcessNode.class);
        List<Node> nodeList = new ArrayList<>();
        Node n = new Node(){{
            setNodeId(node.getId());
            setNodeName(node.getName());
        }};
        nodeList.add(n);
        //遍历后续节点
        List<Node> resultList = foreachNode(node.getChildren(), nodeList);
        return resultList;
    }

    @Override
    public Object getForminfo(String id) {
        return formInfoMapper.selectById(id);
    }

    private List<Node> foreachNode(ProcessNode<?> node, List<Node> nodeList) {
        if (!Objects.isNull(node) && !StrUtil.isBlank(node.getId()) && !Objects.isNull(node.getName())) {
            Node n = new Node(){{
                setNodeId(node.getId());
                setNodeName(node.getName());
            }};
            nodeList.add(n);
            if (NodeTypeEnum.CONCURRENTS.equals(node.getType())) {
//                foreachNode(node.getChildren(), nodeList);
                //并行网关，全部需要递归遍历
                node.getBranchs().forEach(branch -> {
                    foreachNode(branch, nodeList);
                });
            }
            foreachNode(node.getChildren(), nodeList);
        }
        return nodeList;
    }

    private void loadProcess(ProcessNode<?> node, Map<String, ProcessNode<?>> nodeMap) {
        if (null != node && null != node.getId()) {
            WFlowToBpmnCreator.coverProps(node);
            nodeMap.put(node.getId(), node);
            if (NodeTypeEnum.CONCURRENTS.equals(node.getType())
                    || NodeTypeEnum.CONDITIONS.equals(node.getType())
                    || NodeTypeEnum.INCLUSIVES.equals(node.getType())) {
                node.getBranchs().forEach(n -> loadProcess(n, nodeMap));
            }
            loadProcess(node.getChildren(), nodeMap);
        }
    }



    private List<WflowFormDir> findChildrenIterative(String parentId) {
        List<WflowFormDir> voList = new ArrayList<>();
        Queue<String> queue = new LinkedList<>();
        queue.offer(parentId);

        while (!queue.isEmpty()) {
            String dirId = queue.poll();
            List<WflowFormDir> dirList = formDirMapper.selectList(
                    new LambdaQueryWrapper<WflowFormDir>().eq(WflowFormDir::getParentId, dirId));
            for (WflowFormDir dir : dirList) {
                WflowFormDir vo = BeanUtil.copyProperties(dir, WflowFormDir.class);
                List<WflowFormDir> childrens = findChildrenIterative(String.valueOf(dir.getId()));
                vo.setChildren(childrens);
                voList.add(vo);
            }
        }
        return voList;
    }
}
