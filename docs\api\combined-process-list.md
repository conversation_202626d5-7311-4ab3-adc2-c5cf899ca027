# 用户综合流程列表API

## 概述

新增的综合流程列表API能够一次性返回当前用户的所有相关流程，包括待办任务、已办任务、发起的流程和抄送的流程。支持按平台应用配置进行过滤。

## API接口

### 请求信息

- **URL**: `/wflow/process/instance/combinedList`
- **方法**: `POST`
- **Content-Type**: `application/json`

### 请求参数

使用 `TodoRequestDto` 作为请求体：

```json
{
  "pageSize": 20,           // 每页数量，默认20
  "pageNo": 1,              // 页码，默认1
  "code": "process_code",   // 流程定义key（可选）
  "startTimes": ["2025-01-01", "2025-01-31"],  // 时间范围（可选）
  "keyword": "关键字",       // 搜索关键字（可选）
  "form": {                 // 表单查询条件（可选）
    "field1": "value1",
    "field2": "value2"
  },
  "platformAppConfId": 1,   // 平台应用配置ID（可选）
  "finished": true,         // 是否已结束（可选）
  "fieldId": "field_id",    // 字段ID（可选）
  "fieldVal": "field_value" // 字段值（可选）
}
```

### 响应数据

返回分页的综合流程列表：

```json
{
  "status": 200,
  "body": {
    "current": 1,
    "size": 20,
    "total": 100,
    "records": [
      {
        "processType": "TODO",           // 流程类型：TODO/DONE/SUBMITTED/CC
        "taskId": "task_123",           // 任务ID（待办和已办时有值）
        "taskName": "审批任务",          // 任务名称
        "taskResult": "APPROVED",       // 任务处理结果（已办时有值）
        "instanceId": "instance_456",   // 流程实例ID
        "processDefId": "process_789",  // 流程定义ID
        "processDefName": "请假流程",    // 流程定义名称
        "instanceName": "张三的请假申请", // 流程实例名称
        "nodeId": "node_001",           // 流程节点ID
        "status": "RUNNING",            // 流程状态
        "result": "RUNNING",            // 流程结果
        "staterUserId": "user_001",     // 发起人用户ID
        "staterUser": {                 // 发起人信息
          "id": "user_001",
          "name": "张三",
          "avatar": "avatar_url"
        },
        "formAbstracts": [              // 表单摘要信息
          {
            "title": "请假类型",
            "value": "年假"
          }
        ],
        "startTime": "2025-01-09T10:00:00",     // 流程开始时间
        "taskCreateTime": "2025-01-09T10:30:00", // 任务创建时间
        "groupId": 1,                   // 流程分组ID
        "groupName": "人事流程"          // 流程分组名称
      }
    ]
  }
}
```

## 流程类型说明

- **TODO**: 待办任务 - 用户需要处理的任务
- **DONE**: 已办任务 - 用户已经处理过的任务  
- **SUBMITTED**: 发起的流程 - 用户作为发起人的流程实例
- **CC**: 抄送流程 - 抄送给用户的流程

## 平台应用配置过滤机制

### 工作原理

1. **配置关联**: 通过 `WflowProcessPlan` 表建立 `platformAppConfId` 与 `groupId` 的关联关系
2. **分组查询**: 根据 `platformAppConfId` 查询出对应的所有 `groupId`
3. **流程过滤**: 只返回属于这些分组的流程实例
4. **兼容性**: 如果不传递 `platformAppConfId`，则返回所有流程（不过滤）

### 数据表关系

```sql
-- 流程方案表：建立应用配置与分组的关联
WflowProcessPlan {
  platformAppConfId: Long,  -- 平台应用配置ID
  groupId: Long,           -- 流程分组ID
  groupName: String        -- 分组名称
}

-- 流程模型表：每个流程都有对应的分组
WflowModels {
  processDefId: String,    -- 流程定义ID
  groupId: Long           -- 所属分组ID
}
```

## 功能特性

1. **统一查询**: 一次API调用获取所有类型的流程数据
2. **应用过滤**: 支持按平台应用配置进行过滤，基于 `WflowProcessPlan` 表的关联关系
3. **分组信息**: 自动填充分组ID和分组名称，便于前端展示
4. **多条件搜索**: 支持按时间、关键字、表单字段等条件搜索
5. **分页支持**: 支持分页查询，提高性能
6. **类型标识**: 每条记录都有明确的类型标识
7. **时间排序**: 按时间倒序排列，最新的流程在前
8. **权限控制**: 继承现有的流程权限体系，确保数据安全

## 使用示例

### 获取所有相关流程

```bash
curl -X POST "http://localhost:8080/wflow/process/instance/combinedList" \
  -H "Content-Type: application/json" \
  -d '{
    "pageSize": 10,
    "pageNo": 1
  }'
```

### 按应用配置过滤

```bash
curl -X POST "http://localhost:8080/wflow/process/instance/combinedList" \
  -H "Content-Type: application/json" \
  -d '{
    "pageSize": 10,
    "pageNo": 1,
    "platformAppConfId": 1
  }'
```

### 按关键字搜索

```bash
curl -X POST "http://localhost:8080/wflow/process/instance/combinedList" \
  -H "Content-Type: application/json" \
  -d '{
    "pageSize": 10,
    "pageNo": 1,
    "keyword": "请假",
    "platformAppConfId": 1
  }'
```

## 性能优化

- 使用批量查询减少数据库访问次数
- 预先加载分组名称映射，避免重复查询
- 在内存中进行数据合并和排序
- 支持应用配置过滤减少不必要的数据传输
- 合理的分页机制避免大数据量问题

## 注意事项

1. 该API会查询多个数据源，响应时间可能比单一查询稍长
2. 应用配置过滤基于 `WflowProcessPlan` 表的配置关系
3. 时间排序优先使用任务创建时间，其次使用流程开始时间
4. 总数统计是各类型流程数量的简单相加，可能存在重复计算
5. 如果 `platformAppConfId` 对应的分组配置为空，则不返回任何流程
