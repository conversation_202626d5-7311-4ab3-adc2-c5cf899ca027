package com.wflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wflow.bean.entity.WflowModelPerms;
import com.wflow.bean.entity.WflowModels;
import com.wflow.bean.vo.ModelGroupVo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> willian fu
 * @date : 2022/11/29
 */
public interface WflowModelPermsMapper extends BaseMapper<WflowModelPerms> {

    @Insert({"<script>" +
            "INSERT INTO wflow_model_perms (id, form_id, perm_type, org_id, create_time) VALUES\n" +
            "  <foreach collection =\"perms\" item=\"t\" separator =\",\">\n" +
            "    (#{t.id}, #{t.formId}, #{t.permType}, #{t.orgId}, #{t.createTime})\n" +
            "  </foreach >" +
            "</script>"})
    int insertBatch(@Param("perms") List<WflowModelPerms> tasks);

    @Insert({"<script>" +
            "BEGIN "+
            "  <foreach collection =\"perms\" item=\"t\" separator =\";\">\n" +
            "   INSERT INTO wflow_model_perms (id, form_id, perm_type, org_id, create_time) VALUES\n" +
            "    (#{t.id}, #{t.formId}, #{t.permType}, #{t.orgId}, #{t.createTime})\n" +
            "  </foreach > ;" +
            "END; " +
            "</script>"})
    int insertOracleBatch(@Param("perms") List<WflowModelPerms> tasks);

    @Select("SELECT\n" +
            "  * \n" +
            "FROM\n" +
            "  wflow_models \n" +
            "WHERE\n" +
            "  is_delete = FALSE \n" +
            "  AND is_stop = FALSE \n" +
            "  AND form_id IN (\n" +
            "  SELECT\n" +
            "    form_id \n" +
            "  FROM\n" +
            "    wflow_model_perms \n" +
            "  WHERE\n" +
            "    perm_type = 'user' \n" +
            "    AND org_id = #{userId} UNION ALL\n" +
            "  SELECT P\n" +
            "    .form_id \n" +
            "  FROM\n" +
            "    wflow_model_perms P,\n" +
            "    sys_user d \n" +
            "  WHERE\n" +
            "    d.dept_id::text = P.org_id \n" +
            "    AND P.perm_type = 'dept' \n" +
            "    AND d.ID =  CAST(NULLIF(#{userId}, '') AS bigint) UNION ALL\n" +
            "  SELECT P\n" +
            "    .form_id \n" +
            "  FROM\n" +
            "    wflow_model_perms P,\n" +
            "    sys_user d \n" +
            "  WHERE\n" +
            "    d.dept_id::text = P.org_id \n" +
            "    AND P.perm_type = 'dept' \n" +
            "    AND d.dept_id = CAST(NULLIF(#{userId}, '') AS bigint)  UNION ALL\n" +
            "  SELECT P\n" +
            "    .form_id \n" +
            "  FROM\n" +
            "    wflow_model_perms P,\n" +
            "    sys_user_role d \n" +
            "  WHERE\n" +
            "    d.role_id::text = P.org_id \n" +
            "    AND P.perm_type = 'role' \n" +
            "    AND d.ID =  CAST(NULLIF(#{userId}, '') AS bigint) UNION ALL  \n" +
            " SELECT P\n" +
            "    .form_id \n" +
            "  FROM\n" +
            "    wflow_model_perms P,\n" +
            "    sys_user d \n" +
            "  WHERE\n" +
            "    d.institution_id :: TEXT = P.org_id \n" +
            "    AND P.perm_type = 'institution' \n" +
            "    AND d.id = CAST ( NULLIF ( #{userId}, '' ) AS BIGINT ) \n" +
            "  ) \n" +
            "  OR form_id NOT IN ( SELECT form_id FROM wflow_model_perms ) \n" +
            "ORDER BY\n" +
            "  group_id ASC,\n" +
            "  sort ASC")
    List<ModelGroupVo.Form> selectByPerms(@Param("userId") String userId);
}
