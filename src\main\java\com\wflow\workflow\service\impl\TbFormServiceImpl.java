package com.wflow.workflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wflow.bean.entity.*;
import com.wflow.mapper.*;
import com.wflow.utils.FormVersionUtil;
import com.wflow.workflow.bean.process.form.Form;
import com.wflow.workflow.config.WflowGlobalVarDef;
import com.wflow.workflow.service.FormService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 从 wflow_form_data 表存取表单数据
 *
 * <AUTHOR> willian fu
 * @date : 2024/2/22
 */
@Service("tbFormService")
@Slf4j
public class TbFormServiceImpl extends AbstractFormServiceImpl implements FormService {

    @Autowired
    private WflowFormDataMapper formDataMapper;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private VarFormServiceImpl varFormService;

    @Autowired
    private WflowModelsMapper modelsMapper;

    @Autowired
    private WflowModelHistorysMapper historysMapper;

    @Autowired
    private WflowNodeFormInfoMapper nodeFormInfoMapper;

    @Autowired
    private WflowFormInfoMapper formInfoMapper;

    @Autowired
    private WflowFormInfoHistorysMapper  formInfoHistorysMapper;

    @Override
    public Map<String, Object> getProcessInstanceFormData(String instanceId) {
        Map<String, Object> formData = new HashMap<>();
        formDataMapper.selectList(new LambdaQueryWrapper<WflowFormData>()
                        .eq(WflowFormData::getInstanceId, instanceId))
                .forEach(data -> {
                    String fieldValue = data.getFieldValue();
                    formData.put(data.getFieldName(), data.getIsJson() && fieldValue != null ? JSON.parseObject(fieldValue) : fieldValue);
                });
        return formData;
    }

    @Override
    public Map<String, Object> getFormFieldData(String instanceId, Collection<String> fieldIds) {
        Map<String, Object> formData = new HashMap<>(fieldIds.size());
        formDataMapper.selectList(new LambdaQueryWrapper<WflowFormData>()
                        .eq(WflowFormData::getInstanceId, instanceId)
                        .in(WflowFormData::getFieldName, fieldIds))
                .forEach(data -> {
                    String fieldValue = data.getFieldValue();
                    formData.put(data.getFieldName(), data.getIsJson() && fieldValue != null ? JSON.parseObject(fieldValue) : fieldValue);
                });
        return formData;
    }

    @Override
    public Map<String, Map<String, Object>> getFormFieldDataBatch(Map<String, ? extends Collection<String>> instanceFieldMap) {
        Map<String, Map<String, Object>> result = new HashMap<>();
        //一次性批量查询出来
        Set<String> abstIds = instanceFieldMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(abstIds)) {
            return result;
        }
        List<WflowFormData> instFormDatas = formDataMapper.selectList(new LambdaQueryWrapper<WflowFormData>()
                .in(WflowFormData::getInstanceId, instanceFieldMap.keySet())
                .in(WflowFormData::getFieldId, abstIds));
        //将结果安装result进行分离
        instFormDatas.forEach(data -> {
            Map<String, Object> formData = result.computeIfAbsent(data.getInstanceId(), k -> new HashMap<>());
            String fieldValue = data.getFieldValue();
            formData.put(data.getFieldId(), data.getIsJson() && fieldValue != null ? JSON.parse(fieldValue) : fieldValue);
        });

        return result;
    }

    @Override
    public void saveInstanceFormData(String instanceId, Map<String, Object> formData) {
        ProcessInstance instance = runtimeService.createProcessInstanceQuery().processInstanceId(instanceId).singleResult();
//       // 提取表单字段信息
        WflowModelHistorys models = historysMapper.selectOne(new LambdaQueryWrapper<WflowModelHistorys>()
                .select(WflowModelHistorys::getFormItems)
                .eq(WflowModelHistorys::getProcessDefId, instance.getProcessDefinitionId()));
        boolean flag = FormVersionUtil.getFormVersion(models.getFormItems());
        if(!flag){
            saveFormData(instance,formData);
        }else {
            Map<String, Form> formMap = loadFormItemsMap(models.getFormItems());
            List<WflowFormData> collect = formData.entrySet().stream()
                    .filter(v -> null != v.getValue() && formMap.containsKey(v.getKey())).map(entry -> {
                        Form field = formMap.getOrDefault(entry.getKey(), new Form());
                        //存储表单数据
                        return WflowFormData.builder()
                                .id(IdUtil.objectId())
                                .instanceId(instanceId)
                                .defId(instance.getProcessDefinitionId())
                                .createTime(instance.getStartTime())
                                .updateTime(instance.getStartTime())
                                .code(instance.getProcessDefinitionKey())
                                .fieldId(entry.getKey())
                                .fieldKey(field.getKey())
                                .fieldName(field.getTitle())
                                .fieldType(field.getName())
                                .fieldValue(coverToString(entry.getValue()))
                                .isJson(!isSimpleType(entry.getValue()))
                                .build();
                    }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                if (DbType.MYSQL.equals(WflowGlobalVarDef.DB_TYPE)){
                    formDataMapper.insertBatch(collect);
                }else if (DbType.ORACLE.equals(WflowGlobalVarDef.DB_TYPE)){
                    formDataMapper.insertOracleBatch(collect);
                }else if (DbType.POSTGRE_SQL.equals(WflowGlobalVarDef.DB_TYPE)){
                    formDataMapper.insertPostgreBatch(collect);
                }
            }
        }
    }
    //保存表单
    public void saveFormData(ProcessInstance instance,Map<String, Object>  formData){
        List<WflowFormData> collect = doForm(instance,formData);
        if (CollectionUtil.isNotEmpty(collect)) {
            if (DbType.MYSQL.equals(WflowGlobalVarDef.DB_TYPE)){
                formDataMapper.insertBatch(collect);
            }else if (DbType.ORACLE.equals(WflowGlobalVarDef.DB_TYPE)){
                formDataMapper.insertOracleBatch(collect);
            }else if (DbType.POSTGRE_SQL.equals(WflowGlobalVarDef.DB_TYPE)){
                formDataMapper.insertPostgreBatch(collect);
            }
        }

    }

    @Override
    public List<WflowFormRecord> updateInstanceFormData(String userId, String instanceId, Map<String, Object> formData) {
        if (CollectionUtil.isEmpty(formData)) {
            return Collections.emptyList();
        }
        List<WflowFormRecord> records = new ArrayList<>(formData.size());
        Date time = GregorianCalendar.getInstance().getTime();
        //先查询出来所有的表单字段
        Set<String> hasKeys = formDataMapper.selectList(new LambdaQueryWrapper<WflowFormData>()
                        .select(WflowFormData::getFieldId).eq(WflowFormData::getInstanceId, instanceId)
                        .in(WflowFormData::getFieldId, formData.keySet()))
                .stream().map(WflowFormData::getFieldId).collect(Collectors.toSet());
        //把新增的字段和需要更新的字段数据分成2个map集合
        Map<String, Object> addData = new HashMap<>();
        Map<String, Object> updateData = new HashMap<>();
                formData.forEach((id, val) -> (hasKeys.contains(id) ? updateData : addData).put(id, val));
        if (CollectionUtil.isNotEmpty(addData)){
            saveInstanceFormData(instanceId, addData);
            addData.forEach((id, val) -> records.add(WflowFormRecord.builder()
                    .fieldId(id).instanceId(instanceId)
                    .oldValue(null)
                    .updateBy(userId)
                    .id(IdUtil.objectId())
                    .createTime(time)
                    .newValue(coverToString(val)).build()));
        }
        if (CollectionUtil.isNotEmpty(updateData)) {
            formData.forEach((id, val) -> {
                WflowFormData data = new WflowFormData();
                data.setInstanceId(instanceId);
                data.setFieldId(id);
                data.setFieldValue(coverToString(val));
                formDataMapper.update(data, new LambdaQueryWrapper<WflowFormData>()
                        .eq(WflowFormData::getInstanceId, instanceId)
                        .eq(WflowFormData::getFieldId, id));
                records.add(WflowFormRecord.builder()
                        .fieldId(id).instanceId(instanceId)
                        .oldValue(coverToString(val))
                        .updateBy(userId)
                        .id(IdUtil.objectId())
                        .createTime(time)
                        .newValue(coverToString(val)).build());
            });
        }
        return records;
    }

    @Override
    public Map<String, Object> getProcessNodeFormData(String instanceId, String nodeId) {
        Map<String, Object> formData = new HashMap<>();
        formDataMapper.selectList(new LambdaQueryWrapper<WflowFormData>()
                        .eq(WflowFormData::getInstanceId, instanceId))
                .forEach(data -> {
                    String fieldValue = data.getFieldValue();
                    formData.put(data.getFieldName(), data.getIsJson() && fieldValue != null ? JSON.parseObject(fieldValue) : fieldValue);
                });
        return formData;
    }
    //查询多表单情况所有表单
    public List<WflowFormData> doForm(ProcessInstance instanceId,Map<String, Object>  formData) {
        // 查询关联的 formInfoId
        List<WflowNodeFormInfo> wflowNodeFormInfoList = nodeFormInfoMapper.selectList(
                Wrappers.<WflowNodeFormInfo>lambdaQuery()
                        .select(WflowNodeFormInfo::getFormInfoId)
                        .eq(StringUtils.isNotBlank(instanceId.getProcessDefinitionId()),WflowNodeFormInfo::getProcessDefId, instanceId.getProcessDefinitionId())
        );


        List<WflowFormInfoHistorys> formInfos = Collections.emptyList();
        if (!wflowNodeFormInfoList.isEmpty()) {
            List<String> formInfoIds = wflowNodeFormInfoList.stream()
                    .map(WflowNodeFormInfo::getFormInfoId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            formInfos = formInfoHistorysMapper.selectList(
                    Wrappers.<WflowFormInfoHistorys>lambdaQuery()
                            .select(WflowFormInfoHistorys::getId, WflowFormInfoHistorys::getFormItems)
                            .in(WflowFormInfoHistorys::getId, formInfoIds)
            );
        }

        Map<String, Form> formNodeMap = new HashMap<>();
        formInfos.forEach(form -> {
            Map<String, Form> fieldMap = loadFormItemsMap(form.getFormItems());
            formNodeMap.putAll(fieldMap);
        });

        return formData.entrySet().stream()
                .filter(v -> v.getValue() != null && formNodeMap.containsKey(v.getKey()))
                .map(entry -> {
                    Form field = formNodeMap.getOrDefault(entry.getKey(), new Form());
                    return buildFormData(instanceId, entry,field);
                })
                .collect(Collectors.toList());
    }
    // 辅助方法：构建带节点信息的表单数据
    private WflowFormData buildFormData(ProcessInstance instance,
                                        Map.Entry<String, Object> entry,
                                        Form nodeInfo) {
        return WflowFormData.builder()
                .id(IdUtil.objectId())
                .instanceId(instance.getId())
                .defId(instance.getProcessDefinitionId())
                .createTime(instance.getStartTime())
                .updateTime(instance.getStartTime())
                .code(instance.getProcessDefinitionKey())
                .fieldId(entry.getKey())
                .fieldKey(nodeInfo.getKey())
                .fieldName(nodeInfo.getTitle())
                .fieldType(nodeInfo.getName())
                .fieldValue(coverToString(entry.getValue()))
                .isJson(!isSimpleType(entry.getValue()))
                .build();
    }

}
