package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author：qinyi
 * @Date：2024-11-20 16:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "表单(材料)信息表")
public class WflowFormInfo implements Serializable {

    private static final long serialVersionUID = 478762346225445872L;


    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    //form_items
    @Schema(description ="表单设置内容 JSON")
    private String formItems;

    //material_name
    @Schema(description ="材料名称")
    private String materialName;

    //form_config
    @Schema(description ="表单全局设置 JSON")
    private String formConfig;

    //created
    @Schema(description ="创建时间")
    @TableField(value = "created", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime created;

    //material_show_name
    @Schema(description ="显示名称")
    @TableField("material_show_name")
    private String showNameer;

    //belong_group_id.
//    @Schema(description ="所属分组")
//    private Long belongGroupId;

    //material_sign
    @Schema(description ="材料标识")
    private String materialSign;

    //material_classify
    @Schema(description ="材料分类")
    private String materialClassify;

    //sort
    @Schema(description ="排序")
    private Integer sort;

    //generate
    @Schema(description ="产生")
    private String generate;

    //material_type
    @Schema(description ="材料类型")
    private String materialType;

    //show_position
    @Schema(description ="显示位置")
    private String showPosition;

    //open_with
    @Schema(description ="打开方式")
    private String openWith;

    //constraint
    @Schema(description ="约束")
    private String formConstraint;

    //typesof
//    @Schema(description ="类型")
//    private String typesof;

    //form_dir_id
    @Schema(description ="目录ID")
    private String formDirId;

    @Schema(description ="版本号")
    private Integer version;

    @Schema(description ="是否是固定表单")
    @TableField(exist = false)
    private Boolean fixed;

}
