package com.wflow.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SecureDigestAlgorithm;


import javax.crypto.SecretKey;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/23 16:26 周四
 */
public class JwtTokenUtils {

    /**
     * 加密算法
     */
    private static final SecureDigestAlgorithm<SecretKey, SecretKey> ALGORITHM = Jwts.SIG.HS256;

    /**
     * 私钥 / 生成签名的时候使用的秘钥secret，一般可以从本地配置文件中读取，切记这个秘钥不能外露，只在服务端使用，在任何场景都不应该流露出去。
     * 一旦客户端得知这个secret, 那就意味着客户端是可以自我签发jwt了。
     * 应该大于等于 256位(长度32及以上的字符串)，并且是随机的字符串
     */
    private static final String SECRET = "R9v1a6*F58wR6R1#$%NbJ@%^%x55@mmjcA8^rAHE1U@@O@97KH";

    /**
     * 秘钥实例
     */
    public static final SecretKey KEY = Keys.hmacShaKeyFor(SECRET.getBytes());

    /**
     * 设置过期时间，默认15分钟
     * 现在是 12小时 token过期时间
     * 现在是 366 天
     */
    private static final long EXPIRATION_TIME = 366L * 24 * 60 * 60 * 1000;

    // 生成JWT
    public static String generateToken(String subject) {

        return Jwts.builder()
                .subject(subject)
                .issuedAt(new Date())
                .expiration(new Date(System.currentTimeMillis() + EXPIRATION_TIME))
                .signWith(KEY, ALGORITHM)
                .compact();
    }

    // 解析JWT
    public static Claims parseToken(String token) {

        return Jwts.parser()
                .verifyWith(KEY)
                .build()
                .parseSignedClaims(StrUtil.subSuf(token, 7))
                .getPayload();
    }

    // 验证JWT
    public static boolean validateToken(String token) {
        try {
            if (!StrUtil.startWith(token, "Bearer ")) {
                return false;
            }
            parseToken(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // 示例用法
    public static void main(String[] args) {
        // String token = generateToken("user123");
        // System.out.println("Generated Token: " + token);

        ////////////////////////////////////////////////////////////
//        String token = "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
//
//        System.out.println(StrUtil.subSuf(token, 7));
//
//        if (validateToken(token)) {
//            Claims claims = parseToken(token);
//            System.out.println("Subject: " + claims.getSubject());
//            System.out.println("Token is valid.");
//        } else {
//            System.out.println("Token is invalid.");
//        }
        String token = "Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ7XCJwZXJtaXNzaW9uc1wiOltcInN5czp3cml0ZVwiLFwic3lzOmRlbFwiLFwic3lzOnJlYWRcIl0sXCJ1c2VyXCI6e1wiYWN0aXZlU3RhdHVzXCI6MSxcImNyZWF0ZVRpbWVcIjpcIjIwMjMtMTEtMzAgMTc6NTQ6MDBcIixcImRlcHRJZFwiOjE3Mjc5NjgyNTYwNDg3MTc4MjYsXCJpZFwiOjE3MzAxNjMyNTcyMzA5MzgxMTMsXCJpbnN0aXR1dGlvbklkXCI6MTcyNzk0MTg2NDAzNjY3NTU4NSxcImlzTG9ja1wiOjAsXCJuaWNrTmFtZVwiOlwi6LaF57qn566h55CG5ZGYXCIsXCJvcGVyYXRlclwiOlwicWlueWlcIixcInBhc3N3b3JkXCI6XCIkMmEkMTAkWHJWd1ZGeC9xRmRUczduN0VmaS9PT3I1Q2JDNE9GWHdVVWtyL29SNUtSVENaSXcubS9KaXVcIixcInB3ZFVwZGF0ZURhdGVcIjpcIjIwMjQtMDItMjkgMDk6NTU6MzJcIixcInJlZ2lzdGVyVGltZVwiOlwiMjAyMy0xMS0zMCAxNzo1NDowMFwiLFwicmVnaXN0ZXJVc2VyXCI6XCJxaW55aVwiLFwic2hvd1NvcnRcIjozLFwidXBkYXRlVGltZVwiOlwiMjAyNC0wMy0xOSAwOToxMjoyMFwiLFwidXNlcm5hbWVcIjpcInFpbnlpXCJ9fSIsImlhdCI6MTczMDE4NjYwOCwiZXhwIjoxNzYxODA5MDA4fQ.545Ax12g3BoL3wFsiX2apO5Qg04t8M8VH_r5iocNSa8";
        Claims claims = parseToken(token);
        System.out.println(JSONObject.parseObject(JSONObject.parseObject(claims.getSubject()).get("user").toString()).get("id"));
    }
}
