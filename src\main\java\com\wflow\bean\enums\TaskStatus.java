package com.wflow.bean.enums;

/**
 * 异步任务状态枚举
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
public enum TaskStatus {
    
    /**
     * 等待处理
     */
    PENDING("PENDING", "等待处理"),
    
    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 处理失败
     */
    FAILED("FAILED", "处理失败"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),
    
    /**
     * 超时
     */
    TIMEOUT("TIMEOUT", "处理超时");
    
    private final String code;
    private final String description;
    
    TaskStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static TaskStatus fromCode(String code) {
        for (TaskStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown task status code: " + code);
    }
    
    /**
     * 判断是否为终态
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == CANCELLED || this == TIMEOUT;
    }
    
    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMPLETED;
    }
}
