package com.wflow.bean.vo;

import com.alibaba.fastjson2.JSONObject;
import com.wflow.workflow.bean.process.form.Form;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WflowFormListVo {
    private String formId;
    private String formName;
    private JSONObject formConfig;
    private List<Form> formItems;
    private String dirId;
}
