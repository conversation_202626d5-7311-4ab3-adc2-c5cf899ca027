package com.wflow.workflow.bean.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> willian fu
 * @date : 2022/9/19
 */
@Data
@Builder
@AllArgsConstructor
public class InstanceCountVo {
    //待我处理
    private Long todo;
    //我提交并且未完成的
    private Long mySubmited;
    //抄送我的
    private Integer cc;
    //我已处理未办结
    private  Long processed;
    //我已处理已办结
    private Long end;
}
