package com.wflow.utils;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.wflow.bean.do_.UserDo;
import com.wflow.bean.vo.UserVo;
import com.wflow.service.OrgRepositoryService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Optional;

/**
 * <AUTHOR> willian fu
 * @date : 2022/9/29
 */
@Slf4j
@Component
public class UserUtil {

    /**
     * 获取当前登录用户的id
     * @return 用户ID
     */
    @Resource
    private OrgRepositoryService orgRepositoryService;

    public static String getLoginUserId(){
        return StpUtil.getLoginIdAsString();
    }

    /**
     * 获取当前用户租户ID
     * @return 租户ID
     */
    public static String getLoginTenantId(){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        return Optional.ofNullable(request.getHeader("TenantId")).orElse("default");
    }

    /**
     * 获取当前登录用户的行政区编码
     * 
     * @return 行政区编码，如果获取失败则返回全国代码 "000000000000"
     */
    public String getCurrentUserXzqCode() {
        try {
            String userId = getLoginUserId();
            if (StrUtil.isBlank(userId)) {
                log.warn("当前用户未登录，使用默认行政区编码");
                return "000000000000"; // 全国代码
            }

            UserVo userDetail = orgRepositoryService.getUserDetail(userId);
            if (userDetail != null && StrUtil.isNotBlank(userDetail.getDistrictCode())) {
                // 清理districtCode中的多余字符（如方括号等）
                String districtCode = userDetail.getDistrictCode()
                        .replace("[", "")
                        .replace("]", "")
                        .trim();

                log.debug("获取到当前用户行政区编码: {}", districtCode);
                return districtCode;
            } else {
                log.warn("无法获取用户行政区编码，用户ID: {}, 使用默认编码", userId);
                return "000000000000"; // 全国代码
            }
        } catch (Exception e) {
            log.error("获取当前用户行政区编码失败", e);
            return "000000000000"; // 全国代码
        }
    }

    /**
     * 获取当前登录用户的行政区编码（静态方法）
     * 需要通过Spring容器获取UserUtil实例
     *
     * @return 行政区编码
     */
    public static String getCurrentUserXzqCodeStatic() {
        try {
            // 通过Spring上下文获取UserUtil实例
            UserUtil userUtil = BeanUtil.getBean(UserUtil.class);
            return userUtil.getCurrentUserXzqCode();
        } catch (Exception e) {
            // 如果获取失败，返回默认值
            return "000000000000";
        }
    }

}
