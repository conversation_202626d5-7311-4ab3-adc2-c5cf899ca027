package com.wflow.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 异步任务线程池
     */
//    @Bean("taskExecutors")
//    public Executor taskExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//
//        // 核心线程数
//        executor.setCorePoolSize(5);
//
//        // 最大线程数
//        executor.setMaxPoolSize(20);
//
//        // 队列容量
//        executor.setQueueCapacity(100);
//
//        // 线程空闲时间（秒）
//        executor.setKeepAliveSeconds(60);
//
//        // 线程名前缀
//        executor.setThreadNamePrefix("async-task-");
//
//        // 拒绝策略：由调用线程处理
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//
//        // 等待所有任务结束后再关闭线程池
//        executor.setWaitForTasksToCompleteOnShutdown(true);
//
//        // 等待时间（秒）
//        executor.setAwaitTerminationSeconds(60);
//
//        // 初始化
//        executor.initialize();
//
//        log.info("异步任务线程池初始化完成，核心线程数: {}, 最大线程数: {}, 队列容量: {}",
//                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
//
//        return executor;
//    }

    /**
     * 统计任务专用线程池
     */
    @Bean("statisticsExecutor")
    public Executor statisticsExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(3);
        
        // 最大线程数
        executor.setMaxPoolSize(10);
        
        // 队列容量
        executor.setQueueCapacity(50);
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 线程名前缀
        executor.setThreadNamePrefix("statistics-task-");
        
        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化
        executor.initialize();
        
        log.info("统计任务线程池初始化完成，核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
}
