package com.wflow.bean.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
/**
    * 节点表单绑定表
    */
public class WflowModelGroup {
    /**
    * 主键
    */
    private String id;

    /**
    * 流程模型ID
    */
    private String modelId;

    /**
    * 分组ID
    */
    private String groupId;

    /**
    * 创建时间
    */
    private Date createTime;
}