package com.wflow.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.vo.AsyncTaskVo;
import com.wflow.bean.vo.LandAcquisitionStatisticsVo;
import com.wflow.service.AsyncTaskService;
import com.wflow.utils.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 异步任务管理Controller
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@RestController
@RequestMapping("/wflow/async")
@Tag(name = "异步任务管理", description = "异步任务提交、查询、管理接口")
public class AsyncTaskController {

    @Autowired
    private AsyncTaskService asyncTaskService;

    @GetMapping("/task/{taskId}")
    @Operation(summary = "查询任务状态", description = "根据任务ID查询异步任务的执行状态和结果")
    public Object getTaskStatus(
            @Parameter(description = "任务ID", required = true) @PathVariable String taskId) {

        try {
            AsyncTaskVo taskVo = asyncTaskService.getTaskStatus(taskId);

            if (taskVo == null) {
                return R.error("任务不存在或已过期");
            }

            return R.ok(taskVo);

        } catch (Exception e) {
            log.error("查询任务状态失败，任务ID: {}", taskId, e);
            return R.error("查询任务状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/task/{taskId}/cancel")
    @Operation(summary = "取消任务", description = "取消指定的异步任务")
    public Object cancelTask(
            @Parameter(description = "任务ID", required = true) @PathVariable String taskId) {

        try {
            boolean success = asyncTaskService.cancelTask(taskId);

            if (success) {
                return R.ok("任务已取消");
            } else {
                return R.error("取消任务失败，任务可能不存在或已完成");
            }

        } catch (Exception e) {
            log.error("取消任务失败，任务ID: {}", taskId, e);
            return R.error("取消任务失败: " + e.getMessage());
        }
    }

    @PostMapping("/task/{taskId}/retry")
    @Operation(summary = "重试任务", description = "重新执行失败的异步任务")
    public Object retryTask(
            @Parameter(description = "任务ID", required = true) @PathVariable String taskId) {

        try {
            boolean success = asyncTaskService.retryTask(taskId);

            if (success) {
                return R.ok("任务已重新提交");
            } else {
                return R.error("重试任务失败，任务可能不存在或不允许重试");
            }

        } catch (Exception e) {
            log.error("重试任务失败，任务ID: {}", taskId, e);
            return R.error("重试任务失败: " + e.getMessage());
        }
    }

    @GetMapping("/tasks/user/{userId}")
    @Operation(summary = "查询用户任务列表", description = "查询指定用户的异步任务列表")
    public Object getUserTasks(
            @Parameter(description = "用户ID", required = true) @PathVariable String userId,
            @Parameter(description = "查询数量限制", required = false) @RequestParam(defaultValue = "20") int limit) {

        try {
            return R.ok(asyncTaskService.getUserTasks(userId, limit));

        } catch (Exception e) {
            log.error("查询用户任务列表失败，用户ID: {}", userId, e);
            return R.error("查询用户任务列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/tasks/statistics")
    @Operation(summary = "获取任务统计信息", description = "获取异步任务的统计信息")
    public Object getTaskStatistics() {

        try {
            return R.ok(asyncTaskService.getTaskStatistics());

        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            return R.error("获取任务统计信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/cleanup")
    @Operation(summary = "清理过期任务", description = "清理过期的已完成任务")
    public Object cleanupExpiredTasks() {

        try {
            int cleanedCount = asyncTaskService.cleanupExpiredTasks();
            return R.ok("清理完成，共清理 " + cleanedCount + " 个过期任务");

        } catch (Exception e) {
            log.error("清理过期任务失败", e);
            return R.error("清理过期任务失败: " + e.getMessage());
        }
    }

    @GetMapping("/task/{taskId}/landStatistics")
    @Operation(summary = "获取征地统计任务结果", description = "获取征地统计异步任务的完整结果数据")
    public Object getLandStatisticsTaskResult(
            @Parameter(description = "任务ID", required = true) @PathVariable String taskId) {

        try {
            Page<LandAcquisitionStatisticsVo> result = asyncTaskService.getLandStatisticsTaskResult(taskId);

            if (result == null || result.getRecords().isEmpty()) {
                return R.error("任务结果不存在或为空");
            }

            return R.ok(result);

        } catch (Exception e) {
            log.error("获取征地统计任务结果失败，任务ID: {}", taskId, e);
            return R.error("获取征地统计任务结果失败: " + e.getMessage());
        }
    }
}
