package com.wflow.bean.vo;

import com.wflow.bean.dto.CoordinateListVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "export")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GeoJsonObjectDto {
    @Schema(description = "坐标列表")
    private List<CoordinateListVo> coordinateList;
    @Schema(description = "地块编号")
    private String landNumber;
    @Schema(description = "地块名称")
    private String landName;
    @Schema(description = "地块面积")
    private String landArea;
    @Schema(description = "界址点数")
    private String landPointNumber;
    @Schema(description = "图形类型")
    private String landType;
    @Schema(description = "导出类型")
    private String exportType;
}
