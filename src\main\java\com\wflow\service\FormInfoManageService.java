package com.wflow.service;

import com.wflow.bean.entity.WflowFormDir;
import com.wflow.bean.entity.WflowFormInfo;
import com.wflow.bean.entity.WflowModelGroups;
import com.wflow.bean.entity.WflowModels;
import com.wflow.bean.vo.ModelGroupVo;

import java.util.List;

/**
 * <AUTHOR> qinyi
 * @date : 2024/11/21
 */
public interface FormInfoManageService {

    void createWflowFormDir(WflowFormDir dir);

    void editWflowFormDir(WflowFormDir dir);

    void delForminfoDir(String id);

    Object getForminfoDirTree();

    void createWflowForm(WflowFormInfo formInfo);

    void editWflowForm(WflowFormInfo formInfo);

    void delWflowForm(String id);

    Object getForminfoList(String materialName, String formDirId,Integer page,Integer pageSize);

    Object getNodeList(String modelHistorysId);

    Object getForminfo(String id);
}
