package com.wflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.wflow.utils.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wflow.bean.entity.WflowNodeOperate;
import com.wflow.mapper.WflowNodeOperateMapper;
import com.wflow.service.WflowNodeOperateService;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024-12-19  10:48
*/
@Slf4j
@Service
@Transactional
public class WflowNodeOperateServiceImpl implements WflowNodeOperateService{

    @Resource
    private WflowNodeOperateMapper wflowNodeOperateMapper;

    @Override
    public Object createNodeOperate(WflowNodeOperate wflowNodeOperate) {
        this.wflowNodeOperateMapper.insert(wflowNodeOperate);
        return R.ok("新增环节操作成功!");
    }

    @Override
    public Object editNodeOperate(WflowNodeOperate wflowNodeOperate) {
        this.wflowNodeOperateMapper.updateById(wflowNodeOperate);
        return R.ok("编辑环节操作成功!");
    }

    @Override
    public Object delNodeOperate(String ids) {
        if(ids.contains(",")){
            List<Long> idList = new ArrayList<>();
            String[] split = ids.split(",");
            Arrays.stream(split).forEach(item->{
                idList.add(Long.parseLong(item));
            });
            this.wflowNodeOperateMapper.deleteBatchIds(idList);
            return R.ok("批量删除环节操作成功!");
        }else{
            this.wflowNodeOperateMapper.deleteById(Long.parseLong(ids));
            return R.ok("删除环节操作成功!");
        }
    }

    @Override
    public Object delNodeOperate2(String nodeId, String operateIds) {
        if(operateIds.contains(",")){
            List<Long> idList = new ArrayList<>();
            String[] split = operateIds.split(",");
            Arrays.stream(split).forEach(item->{
                idList.add(Long.parseLong(item));
            });
            LambdaUpdateWrapper<WflowNodeOperate> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(WflowNodeOperate::getId, idList).set(WflowNodeOperate::getNodeId,null);
            this.wflowNodeOperateMapper.update(wrapper);
            return R.ok("批量删除环节操作成功!");
        }else{
            LambdaUpdateWrapper<WflowNodeOperate> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(WflowNodeOperate::getId, Long.parseLong(operateIds)).set(WflowNodeOperate::getNodeId,null);
            this.wflowNodeOperateMapper.update(wrapper);
            return R.ok("删除环节操作成功!");
        }
    }

    @Override
    public Object innerList(String nodeId, String operateName, String operateSign) {
        LambdaQueryWrapper<WflowNodeOperate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(nodeId),WflowNodeOperate::getNodeId,nodeId)
                .like(StringUtils.isNotBlank(operateName),WflowNodeOperate::getOperateName,operateName)
                .like(StringUtils.isNotBlank(operateSign),WflowNodeOperate::getOperateSign,operateSign)
                .orderByAsc(WflowNodeOperate::getSortNo);
        List<WflowNodeOperate> wflowNodeOperates = this.wflowNodeOperateMapper.selectList(wrapper);
        return R.ok(wflowNodeOperates);
    }

    @Override
    public Object saveToOutList(String nodeOperateIds) {
        if(nodeOperateIds.contains(",")){
            List<Long> idList = new ArrayList<>();
            String[] split = nodeOperateIds.split(",");
            Arrays.stream(split).forEach(item->{
                idList.add(Long.parseLong(item));
            });
            List<WflowNodeOperate> wflowNodeOperates = this.wflowNodeOperateMapper.selectList(new LambdaQueryWrapper<WflowNodeOperate>().in(WflowNodeOperate::getId, idList));
            wflowNodeOperates.forEach(item->{
                item.setIsFinalSave(true);
                this.wflowNodeOperateMapper.updateById(item);
            });
        }else{
            WflowNodeOperate wflowNodeOperate = this.wflowNodeOperateMapper.selectById(Long.valueOf(nodeOperateIds));
            wflowNodeOperate.setIsFinalSave(true);
            this.wflowNodeOperateMapper.updateById(wflowNodeOperate);
        }
        return R.ok("保存成功!");
    }

    @Override
    public Object getNodeOperateList(String nodeId, String operateName, String operateSign) {
        LambdaQueryWrapper<WflowNodeOperate> wrapper = new LambdaQueryWrapper<WflowNodeOperate>().eq(WflowNodeOperate::getNodeId, nodeId).eq(WflowNodeOperate::getIsFinalSave, true);
        wrapper.like(StringUtils.isNotBlank(operateName),WflowNodeOperate::getOperateName,operateName);
        wrapper.like(StringUtils.isNotBlank(operateSign),WflowNodeOperate::getOperateSign,operateSign);
        wrapper.orderByAsc(WflowNodeOperate::getSortNo);
        List<WflowNodeOperate> wflowNodeOperates = this.wflowNodeOperateMapper.selectList(wrapper);
        return R.ok(wflowNodeOperates);
    }

    @Override
    public Object delOutNodeOperate(String ids) {
        if(ids.contains(",")){
            List<Long> idList = new ArrayList<>();
            String[] split = ids.split(",");
            Arrays.stream(split).forEach(item->{
                idList.add(Long.parseLong(item));
            });
            List<WflowNodeOperate> wflowNodeOperates = this.wflowNodeOperateMapper.selectBatchIds(idList);
            wflowNodeOperates.forEach(item->{
                item.setIsFinalSave(false);
                this.wflowNodeOperateMapper.updateById(item);
            });
        }else{
            WflowNodeOperate wflowNodeOperate = this.wflowNodeOperateMapper.selectById(Long.valueOf(ids));
            wflowNodeOperate.setIsFinalSave(false);
            this.wflowNodeOperateMapper.updateById(wflowNodeOperate);
        }
        return R.ok("操作成功!");
    }

    @Override
    public Object copyFrom(String nodeId, String fromNodeId,Long modelHistorysId) {
        List<WflowNodeOperate> wflowNodeOperates = this.wflowNodeOperateMapper.selectList(new LambdaQueryWrapper<WflowNodeOperate>().eq(WflowNodeOperate::getNodeId, fromNodeId));
        wflowNodeOperates.forEach(item->{
            item.setId(null);
            item.setNodeId(nodeId);
            item.setModelHistorysId(modelHistorysId);
            item.setCreated(LocalDateTime.now());
            this.wflowNodeOperateMapper.insert(item);
        });
        return R.ok("复制成功!");
    }

    @Override
    public Object relationOperaToNode(String nodeId, String operateIds) {
        if(operateIds.contains(",")){
            List<Long> idList = new ArrayList<>();
            String[] split = operateIds.split(",");
            Arrays.stream(split).forEach(item->{
                idList.add(Long.parseLong(item));
            });
            LambdaUpdateWrapper<WflowNodeOperate> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(WflowNodeOperate::getNodeId,nodeId).in(WflowNodeOperate::getId,idList);
            this.wflowNodeOperateMapper.update(wrapper);
        }else{
            LambdaUpdateWrapper<WflowNodeOperate> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(WflowNodeOperate::getNodeId,nodeId).eq(WflowNodeOperate::getId,Long.parseLong(operateIds));
            this.wflowNodeOperateMapper.update(wrapper);
        }
        return R.ok("关联操作到节点成功!");
    }

}
