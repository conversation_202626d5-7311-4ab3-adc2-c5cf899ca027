package com.wflow.workflow.controller;

import com.wflow.utils.R;
import com.wflow.workflow.service.ProcessExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 流程数据导出控制器
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@RestController
@RequestMapping("/wflow/process/export")
@Tag(name = "流程数据导出", description = "流程数据导出相关接口")
public class ProcessExportController {

    @Autowired
    private ProcessExportService processExportService;

    /**
     * 导出流程数据
     */
    @GetMapping("/{instanceId}")
    @Operation(summary = "导出流程数据", description = "导出指定流程实例的完整数据，包括表单数据、审批记录和附件")
    public void exportProcessData(
            @Parameter(description = "流程实例ID", required = true)
            @PathVariable String instanceId,
            @Parameter(description = "当前节点ID", required = false)
            @RequestParam(required = false) String nodeId,
            @Parameter(description = "导出原因", required = false)
            @RequestParam(required = false) String reason,
            HttpServletResponse response) {
        
        try {
            log.info("开始导出流程数据，实例ID: {}, 节点ID: {}, 导出原因: {}", instanceId, nodeId, reason);
            
            processExportService.exportProcessData(instanceId, nodeId, response);
            
            log.info("流程数据导出完成，实例ID: {}", instanceId);
            
        } catch (Exception e) {
            log.error("导出流程数据失败，实例ID: {}", instanceId, e);
            try {
                response.reset();
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("返回错误信息失败", ex);
            }
        }
    }

}
