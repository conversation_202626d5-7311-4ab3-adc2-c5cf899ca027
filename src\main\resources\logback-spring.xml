<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
  
  <property name="appName" value="wflow-pro" />
  <property name="logPath" value="logs/${app_name}" />
  
  <contextName>logback</contextName>
  
  <!--输出到控制台 ConsoleAppender-->
  <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
    <!--展示格式 layout-->
    <layout class="ch.qos.logback.classic.PatternLayout">
      <pattern>
        <pattern>%d{HH:mm:ss} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
      </pattern>
    </layout>
    <!--
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
         <level>ERROR</level>
    </filter>
     -->
  </appender>
  
  <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!--如果只是想要 Info 级别的日志，只是过滤 info 还是会输出 Error 日志，因为 Error 的级别高，
    所以我们使用下面的策略，可以避免输出 Error 的日志-->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <!--日志名称，如果没有File 属性，那么只会使用FileNamePattern的文件路径规则
        如果同时有<File>和<FileNamePattern>，那么当天日志是<File>，明天会自动把今天
        的日志改名为今天的日期。即，<File> 的日志都是当天的。
    -->
    <!--<File>logPath/info.appName.log</File>-->
    <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
      <FileNamePattern>${logPath}/%d{yyyy-MM-dd}/info.log</FileNamePattern>
      <!--只保留最近90天的日志-->
      <maxHistory>90</maxHistory>
      <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
      <!--<totalSizeCap>1GB</totalSizeCap>-->
    </rollingPolicy>
    <!--日志输出编码格式化-->
    <encoder>
      <charset>UTF-8</charset>
      <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>
  
  <appender name="fileWarnLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>WARN</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
      <FileNamePattern>${logPath}/%d{yyyy-MM-dd}/warn.log</FileNamePattern>
      <!--只保留最近90天的日志-->
      <maxHistory>90</maxHistory>
      <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
      <!--<totalSizeCap>1GB</totalSizeCap>-->
    </rollingPolicy>
    <!--日志输出编码格式化-->
    <encoder>
      <charset>UTF-8</charset>
      <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>
  
  <appender name="fileErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!--如果只是想要 Error 级别的日志，那么需要过滤一下，默认是 info 级别的，ThresholdFilter-->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
      <FileNamePattern>${logPath}/%d{yyyy-MM-dd}/error.log</FileNamePattern>
      <!--只保留最近90天的日志-->
      <maxHistory>365</maxHistory>
      <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
      <!--<totalSizeCap>1GB</totalSizeCap>-->
    </rollingPolicy>
    <!--日志输出编码格式化-->
    <encoder>
      <charset>UTF-8</charset>
      <pattern>%d{HH:mm:ss} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>
  
  <!--防止日志重复输出-->
  <logger name="com.thomson.devops" additivity="false" level="info">
    <!--appender将会添加到这个loger-->
    <appender-ref ref="fileWarnLog"/>
    <appender-ref ref="fileInfoLog"/>
    <appender-ref ref="fileErrorLog"/>
    <appender-ref ref="consoleLog"/>
  </logger>
  
  <!--指定最基础的日志输出级别-->
  <!-- <root level="INFO">
     &lt;!&ndash;appender将会添加到这个loger&ndash;&gt;
     <appender-ref ref="fileWarnLog"/>
     <appender-ref ref="fileInfoLog"/>
     <appender-ref ref="fileErrorLog"/>
     <appender-ref ref="consoleLog"/>
   </root>-->
  
  <!-- 开发、默认环境 只输出到控制台 -->
  <springProfile name="dev">
    <root level="INFO">
      <appender-ref ref="fileInfoLog" />
      <appender-ref ref="fileWarnLog" />
      <appender-ref ref="fileErrorLog" />
      <appender-ref ref="consoleLog"/>
    </root>
  </springProfile>
  
  <!-- 正式环境 输出INFO及以上日志 -->
  <springProfile name="!dev">
    <root level="INFO">
      <appender-ref ref="fileInfoLog" />
      <appender-ref ref="fileWarnLog" />
      <appender-ref ref="fileErrorLog" />
    </root>
  </springProfile>
  
</configuration>
