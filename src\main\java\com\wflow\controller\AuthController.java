package com.wflow.controller;

import com.wflow.bean.vo.UserLoginVo;
import com.wflow.service.AuthService;
import com.wflow.utils.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.io.IOException;

/**
 * <AUTHOR> willian fu
 * @date : 2022/8/15
 */
@RestController
@RequestMapping("sys/auth")
//@CrossOrigin(origins = {"http://192.168.3.30", "https://192.168.3.133"}, allowCredentials = "true")
@Tag(name = "登录")
public class AuthController {

    @Autowired
    private AuthService authService;

    /**
     * 获取登录验证码，暂未使用
     * @param request 请求
     * @param response 响应
     * @throws IOException
     */
    @GetMapping("login/code")
    @Operation(summary ="获取登录验证码，暂未使用")
    public void getLoginCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        authService.getLoginCode(request, response);
    }

    /**
     * 用户登录，暂未使用
     * @param request 请求
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @return 登录结果
     */
    @GetMapping("login")
    @Operation(summary ="用户登录，暂未使用")
    public Object userLogin(HttpServletRequest request,
                            @RequestParam String username,
                            @RequestParam String password,
                            @RequestParam String code) {
        UserLoginVo loginVo = authService.userLogin(request, username, password, code);
        return R.ok(loginVo);
    }

    /**
     * 忽略账号密码快速登录进行用户切换
     * @param userId 用户ID
     * @return 登录用户的信息
     */
    @GetMapping("login/ignore/{userId}")
    @Operation(summary ="忽略账号密码快速登录进行用户切换")
    public Object userLoginIgnore(@PathVariable String userId) {
        UserLoginVo loginVo = authService.userLoginIgnore(userId);
        return R.ok(loginVo);
    }

    /**
     * 忽略账号密码快速登录进行用户切换
     * @param userId 业务系统传来的token
     * @return 登录用户的信息
     */
    @PostMapping("login/cross-system")
    @Operation(summary ="忽略账号密码快速登录进行用户切换")
    public Object userLogin(@RequestParam("userId") String userId) {
        UserLoginVo loginVo = authService.userLoginIgnore(userId);
        return R.ok(loginVo);
    }
}
