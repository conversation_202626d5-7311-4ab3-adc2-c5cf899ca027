package com.wflow.bean.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Schema(description = "坐标列表")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CoordinateListVo {

    @Schema(description = "界址点号")
    @Excel(name = "界址点号",needMerge = true)
    private String name;
    @Excel(name = "纵坐标",needMerge = true)
    @Schema(description = "纵坐标")
    private String x;
    @Excel(name = "横坐标",needMerge = true)
    @Schema(description = "横坐标")
    private String y;
    @Excel(name = "反算边长",needMerge = true)
    @Schema(description = "边长")
    private String length;
    @Excel(name = "地块圈号",needMerge = true)
    @Schema(description = "地块圈号")
    private String number;
    @Excel(name = "备注",needMerge = true)
    @Schema(description = "备注")
    private String remark;
}
