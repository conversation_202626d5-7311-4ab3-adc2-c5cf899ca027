package com.wflow.workflow.service;

import com.wflow.workflow.bean.vo.ProcessExportVo;
import jakarta.servlet.http.HttpServletResponse;


/**
 * 流程数据导出服务接口
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
public interface ProcessExportService {

    /**
     * 导出流程数据（Excel + 附件打包）
     * 
     * @param instanceId 流程实例ID
     * @param nodeId 当前节点ID
     * @param response HTTP响应对象
     * @throws Exception 导出异常
     */
    void exportProcessData(String instanceId, String nodeId, HttpServletResponse response) throws Exception;


    /**
     * 获取流程导出数据
     * 
     * @param instanceId 流程实例ID
     * @return 导出数据VO
     */
    ProcessExportVo getProcessExportData(String instanceId);

    /**
     * 生成Excel文件
     * 
     * @param exportData 导出数据
     * @return Excel文件的字节数组
     * @throws Exception 生成异常
     */
    byte[] generateExcelFile(ProcessExportVo exportData) throws Exception;

    /**
     * 打包Excel和附件文件
     * 
     * @param excelBytes Excel文件字节数组
     * @param exportData 导出数据（包含附件信息）
     * @return ZIP文件的字节数组
     * @throws Exception 打包异常
     */
    byte[] packageFiles(byte[] excelBytes, ProcessExportVo exportData) throws Exception;
}
