package com.wflow.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wflow.bean.entity.WflowFormData;
import com.wflow.bean.vo.LandAcquisitionStatisticsVo;
import com.wflow.mapper.WflowFormDataMapper;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单数据过滤器工具类
 * 用于根据WflowFormData表中的字段值进行过滤
 *
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
public class FilterUtils {

    /**
     * 构建WflowFormData的查询条件，用于过滤表单数据
     *
     * @param filters 过滤条件Map，key为fieldId，value为要匹配的fieldValue
     * @return 包含过滤条件的实例ID集合，如果没有过滤条件则返回null
     */
    public static Set<String> buildFormDataFilterConditions(Map<String, Object> filters) {

        if (filters == null || filters.isEmpty()) {
            log.debug("没有过滤条件，跳过表单数据过滤");
            return null;
        }

        log.debug("开始构建表单数据过滤条件，过滤字段数: {}", filters.size());

        // 存储每个过滤条件匹配的实例ID集合
        List<Set<String>> filterResults = new ArrayList<>();

        for (Map.Entry<String, Object> filter : filters.entrySet()) {
            String fieldId = filter.getKey();
            Object fieldValue = filter.getValue();

            if (fieldValue == null) {
                log.debug("跳过空值过滤条件，字段ID: {}", fieldId);
                continue;
            }

            Set<String> matchingInstanceIds = getInstanceIdsByFieldFilter(fieldId, fieldValue.toString());
            filterResults.add(matchingInstanceIds);

            log.debug("字段过滤结果 - 字段ID: {}, 字段值: {}, 匹配实例数: {}",
                    fieldId, fieldValue, matchingInstanceIds.size());
        }

        if (filterResults.isEmpty()) {
            log.debug("所有过滤条件都为空，跳过过滤");
            return null;
        }

        // 计算所有过滤条件的交集（AND逻辑）
        Set<String> finalInstanceIds = filterResults.get(0);
        for (int i = 1; i < filterResults.size(); i++) {
            finalInstanceIds = finalInstanceIds.stream()
                    .filter(filterResults.get(i)::contains)
                    .collect(Collectors.toSet());
        }

        log.debug("过滤条件AND运算结果，最终匹配实例数: {}", finalInstanceIds.size());

        return finalInstanceIds;
    }

    /**
     * 根据字段ID和字段值查询匹配的实例ID集合
     * 
     * @param fieldId    字段ID
     * @param fieldValue 字段值
     * @return 匹配的实例ID集合
     */
    private static Set<String> getInstanceIdsByFieldFilter(String fieldId, String fieldValue) {
        try {
            // 通过BeanUtil获取WflowFormDataMapper实例
            WflowFormDataMapper formDataMapper = BeanUtil.getBean(WflowFormDataMapper.class);

            LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WflowFormData::getFieldId, fieldId)
                    .eq(WflowFormData::getFieldValue, fieldValue);

            List<WflowFormData> matchingData = formDataMapper.selectList(wrapper);

            return matchingData.stream()
                    .map(WflowFormData::getInstanceId)
                    .collect(Collectors.toSet());

        } catch (Exception e) {
            log.error("查询字段过滤数据失败，字段ID: {}, 字段值: {}", fieldId, fieldValue, e);
            return new HashSet<>();
        }
    }

    /**
     * 应用实例ID过滤条件到表单数据查询
     * 
     * @param formDataList        原始表单数据列表
     * @param filteredInstanceIds 过滤后的实例ID集合
     * @return 过滤后的表单数据列表
     */
    public static List<WflowFormData> applyInstanceIdFilter(List<WflowFormData> formDataList,
            Set<String> filteredInstanceIds) {
        if (filteredInstanceIds == null || filteredInstanceIds.isEmpty()) {
            return formDataList;
        }

        return formDataList.stream()
                .filter(data -> filteredInstanceIds.contains(data.getInstanceId()))
                .collect(Collectors.toList());
    }

    /**
     * 构建包含实例ID过滤条件的查询包装器
     * 
     * @param baseWrapper         基础查询条件
     * @param filteredInstanceIds 过滤后的实例ID集合
     * @return 包含过滤条件的查询包装器
     */
    public static LambdaQueryWrapper<WflowFormData> applyInstanceIdFilterToWrapper(
            LambdaQueryWrapper<WflowFormData> baseWrapper, Set<String> filteredInstanceIds) {

        if (filteredInstanceIds != null && !filteredInstanceIds.isEmpty()) {
            baseWrapper.in(WflowFormData::getInstanceId, filteredInstanceIds);
        }

        return baseWrapper;
    }

    /**
     * 字段名映射，处理前端传入的字段名与实体类字段名的差异
     * 
     * @param fieldName 前端传入的字段名
     * @return 实体类中的实际字段名
     */
    private static String mapFieldName(String fieldName) {
        switch (fieldName) {
            case "isCompleted":
                return "isCompleted"; // 假设实体类中有这个字段
            case "landStatusFieldId":
                return "landStatusFieldId"; // 假设实体类中有这个字段
            case "xzqCode":
                return "xzqCode";
            case "xzqName":
                return "xzqName";
            case "approvedLandArea":
                return "approvedLandArea";
            case "verifiedLandArea":
                return "verifiedLandArea";
            case "actualCompletedLandArea":
                return "actualCompletedLandArea";
            case "recordCount":
                return "recordCount";
            default:
                return fieldName; // 默认使用原字段名
        }
    }

    /**
     * 尝试其他可能的字段名
     * 
     * @param item              对象实例
     * @param originalFieldName 原始字段名
     * @return 字段值
     */
    private static Object tryAlternativeFieldNames(LandAcquisitionStatisticsVo item, String originalFieldName)
            throws Exception {
        // 可以在这里添加更多的字段名尝试逻辑
        // 例如：驼峰命名转下划线命名等

        // 如果所有尝试都失败，返回null
        log.warn("无法找到字段: {}", originalFieldName);
        return null;
    }

    /**
     * 比较两个值是否相等
     * 
     * @param actualValue   实际值
     * @param expectedValue 期望值
     * @return 是否相等
     */
    private static boolean compareValues(Object actualValue, Object expectedValue) {
        // 处理null值
        if (actualValue == null && expectedValue == null) {
            return true;
        }
        if (actualValue == null || expectedValue == null) {
            return false;
        }

        // 处理字符串类型
        if (actualValue instanceof String && expectedValue instanceof String) {
            return actualValue.equals(expectedValue);
        }

        // 处理布尔类型
        if (actualValue instanceof Boolean || expectedValue instanceof Boolean) {
            return compareBooleanValues(actualValue, expectedValue);
        }

        // 处理数字类型
        if (isNumericType(actualValue) && isNumericType(expectedValue)) {
            return compareNumericValues(actualValue, expectedValue);
        }

        // 默认使用toString比较
        return actualValue.toString().equals(expectedValue.toString());
    }

    /**
     * 比较布尔值
     */
    private static boolean compareBooleanValues(Object actualValue, Object expectedValue) {
        Boolean actual = convertToBoolean(actualValue);
        Boolean expected = convertToBoolean(expectedValue);

        if (actual == null || expected == null) {
            return false;
        }

        return actual.equals(expected);
    }

    /**
     * 转换为布尔值
     */
    private static Boolean convertToBoolean(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            String str = ((String) value).toLowerCase();
            if ("true".equals(str) || "1".equals(str) || "yes".equals(str)) {
                return true;
            }
            if ("false".equals(str) || "0".equals(str) || "no".equals(str)) {
                return false;
            }
        }
        return null;
    }

    /**
     * 比较数字值
     */
    private static boolean compareNumericValues(Object actualValue, Object expectedValue) {
        try {
            BigDecimal actual = convertToBigDecimal(actualValue);
            BigDecimal expected = convertToBigDecimal(expectedValue);

            if (actual == null || expected == null) {
                return false;
            }

            return actual.compareTo(expected) == 0;
        } catch (Exception e) {
            log.warn("数字比较失败: {} vs {}", actualValue, expectedValue);
            return false;
        }
    }

    /**
     * 转换为BigDecimal
     */
    private static BigDecimal convertToBigDecimal(Object value) {
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 检查是否为数字类型
     */
    private static boolean isNumericType(Object value) {
        return value instanceof Number ||
                (value instanceof String && isNumericString((String) value));
    }

    /**
     * 检查字符串是否为数字
     */
    private static boolean isNumericString(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 从filters中提取特定字段的值
     * 
     * @param filters      过滤条件Map
     * @param fieldName    字段名
     * @param defaultValue 默认值
     * @return 字段值
     */
    public static <T> T getFilterValue(Map<String, Object> filters, String fieldName, T defaultValue) {
        if (filters == null || !filters.containsKey(fieldName)) {
            return defaultValue;
        }

        Object value = filters.get(fieldName);
        if (value == null) {
            return defaultValue;
        }

        try {
            @SuppressWarnings("unchecked")
            T result = (T) value;
            return result;
        } catch (ClassCastException e) {
            log.warn("类型转换失败，字段: {}, 值: {}, 期望类型: {}",
                    fieldName, value, defaultValue.getClass().getSimpleName());
            return defaultValue;
        }
    }
}
