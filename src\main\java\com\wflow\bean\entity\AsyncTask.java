package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wflow.bean.enums.TaskStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 异步任务实体
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("async_task")
public class AsyncTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId(value = "task_id", type = IdType.ASSIGN_ID)
    private String taskId;

    /**
     * 任务类型
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 任务状态
     */
    @TableField("status")
    private TaskStatus status;

    /**
     * 任务进度（0-100）
     */
    @TableField("progress")
    private Integer progress;

    /**
     * 任务优先级（1-10，数字越大优先级越高）
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 任务参数（JSON格式）
     */
    @TableField("parameters")
    private String parameters;

    /**
     * 任务结果（JSON格式）
     */
    @TableField("result")
    private String result;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 预估处理时间（秒）
     */
    @TableField("estimated_time")
    private Integer estimatedTime;

    /**
     * 实际处理时间（秒）
     */
    @TableField("actual_time")
    private Integer actualTime;

    /**
     * 创建用户ID
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 开始处理时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @TableField("max_retry_count")
    private Integer maxRetryCount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 计算实际处理时间
     */
    public void calculateActualTime() {
        if (startTime != null && endTime != null) {
            this.actualTime = (int) java.time.Duration.between(startTime, endTime).getSeconds();
        }
    }

    /**
     * 判断是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return retryCount != null && maxRetryCount != null && retryCount < maxRetryCount;
    }
}
