package com.wflow.service;

import com.wflow.bean.vo.FileResourceVo;
import com.wflow.bean.vo.GeoJsonObjectDto;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;

/**
 * <AUTHOR> willian fu
 * @date : 2022/9/7
 */
public interface FileManagerService {

    /**
     * 上传文件、图片
     * @param file 文件/图片
     * @param isImg 是否为图片
     */
    FileResourceVo uploadFile(MultipartFile file, Boolean isImg, Boolean isSign) throws IOException;

    /**
     * 删除文件
     * @param fileId 文件ID
     */
    void delFileById(String fileId, Boolean isSign);

    /**
     * 通过id获取文件流
     * @param fileId 文件ID
     * @param name 文件名称
     * @param isSign 是否为签名图片
     */
    InputStreamResource getFileById(String fileId, String name, Boolean isSign) throws IOException;

    /*
    * shp文件转geojson
    * */
    Object shpZip2GeoJson(MultipartFile file) throws Exception;
    /*
    * txt文件转geojson
    * */
    Object txt2GeoJsonByGuo(MultipartFile file) throws IOException;

    Object shp2GeoJson(MultipartFile file) throws IOException;

    Object excel2GeoJson(MultipartFile file) throws IOException;


    ResponseEntity<InputStreamResource> exportExcel(GeoJsonObjectDto geoJsonObjectDto, HttpServletRequest request, HttpServletResponse response);

    ResponseEntity<StreamingResponseBody> exportTxt(GeoJsonObjectDto geoJsonObjectDto);
}
