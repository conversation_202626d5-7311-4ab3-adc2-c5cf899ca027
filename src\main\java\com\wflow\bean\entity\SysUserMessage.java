package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
    * 消息中心
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "sys_user_message")
public class SysUserMessage implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    private String content;

    private String title;

    /**
     * 发送用户id
     */
    private Long sendUserId;

    /**
     * 接收用户id
     */
    private Long receiveUserId;

    /**
     * 是否阅读
     */
    private Boolean isRead;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}