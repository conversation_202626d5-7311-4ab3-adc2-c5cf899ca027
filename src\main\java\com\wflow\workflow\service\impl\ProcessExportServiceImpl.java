package com.wflow.workflow.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wflow.bean.do_.UserDo;
import com.wflow.bean.entity.*;
import com.wflow.mapper.WflowConfigurationfMapper;
import com.wflow.mapper.WflowFormDataMapper;
import com.wflow.mapper.WflowModelsMapper;
import com.wflow.service.OrgRepositoryService;
import com.wflow.service.impl.FormGroupServiceImpl;
import com.wflow.utils.UserUtil;

import com.wflow.workflow.bean.vo.FileVo;
import com.wflow.workflow.bean.vo.ProcessExportVo;
import com.wflow.workflow.service.BusinessDataStorageService;
import com.wflow.workflow.service.ProcessExportService;
import com.wflow.workflow.service.ProcessInstanceService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;

import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;

import java.io.FileInputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.wflow.service.impl.LocalFileManagerServiceImpl.getString;

/**
 * 流程数据导出服务实现类
 *
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@Service
public class ProcessExportServiceImpl implements ProcessExportService {

    //默认文件存储路径
    private static final String BASE_DIR_FILE = "resource/file/";
    //操作系统信息
    private final static String OS = System.getProperty("os.name").toLowerCase();
    @Resource
    private HistoryService historyService;


    @Resource
    private WflowFormDataMapper formDataMapper;

    @Resource
    private OrgRepositoryService orgRepositoryService;

    @Resource
    private ProcessExportExcelHelper excelHelper;

    @Resource
    private BusinessDataStorageService businessDataService;

    @Resource
    private FormGroupServiceImpl formGroupService;

    @Resource
    private WflowConfigurationfMapper configurationMapper;

    @Resource
    private VarFormServiceImpl varFormService;

    @Override
    public void exportProcessData(String instanceId, String nodeId, HttpServletResponse response) throws Exception {
        // 1. 权限检查
        String currentUserId = UserUtil.getLoginUserId();

        // 2. 获取导出数据
        ProcessExportVo exportData = getProcessExportData(instanceId);
        exportData.setExportUser(currentUserId);
        UserDo user = orgRepositoryService.getUserById(currentUserId);
        exportData.setExportUserName(user.getUserName());
        exportData.setExportTime(LocalDateTime.now());

        // 3. 生成Excel文件
        byte[] excelBytes = generateExcelFile(exportData);

        // 4. 打包文件
        byte[] zipBytes = packageFiles(excelBytes, exportData);

        // 5. 设置响应头并输出文件
        String fileName = String.format("流程数据导出_%s_%s.zip",
                exportData.getProcessInfo().getInstanceName(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));

        response.setContentType("application/zip");
        response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        response.setContentLength(zipBytes.length);

        response.getOutputStream().write(zipBytes);
        response.getOutputStream().flush();

        log.info("流程数据导出完成，实例ID: {}, 导出用户: {}, 文件大小: {} bytes",
                instanceId, currentUserId, zipBytes.length);
    }


    @Override
    public ProcessExportVo getProcessExportData(String instanceId) {
        try {
            // 1. 获取流程基本信息
            ProcessExportVo.ProcessBasicInfo processInfo = getProcessBasicInfo(instanceId);
            // 2. 获取表单
            List<ProcessExportVo.FormDataInfo> formDefinitions = getFormDefinitions(instanceId);
            //获取附件信息
            List<ProcessExportVo.AttachmentInfo> attachmentInfos = getAttachments(instanceId);
            return ProcessExportVo.builder()
                    .attachments(attachmentInfos)
                    .formData(formDefinitions)
                    .processInfo(processInfo)
                    .build();

        } catch (Exception e) {
            log.error("获取流程导出数据失败，实例ID: {}", instanceId, e);
            throw new RuntimeException("获取流程导出数据失败", e);
        }
    }

    /**
     * 获取流程基本信息
     */
    private ProcessExportVo.ProcessBasicInfo getProcessBasicInfo(String instanceId) {
        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(instanceId)
                .singleResult();
        Map<String, Object> formData = new HashMap<>();
        if (processInstance == null) {
            throw new RuntimeException("流程实例不存在: " + instanceId);
        }
        formData = businessDataService.getProcessInstanceFormData(instanceId);
        List<WflowFormDir> formDirs;
        String dirData = Optional.ofNullable(configurationMapper.selectOne(Wrappers.<WflowConfiguration>lambdaQuery()
                        .select(WflowConfiguration::getDirData)
                        .eq(WflowConfiguration::getModelId, instanceId))).map(WflowConfiguration::getDirData)
                .orElse("");
        if(StrUtil.isNotBlank(dirData)) {
            formDirs = JSONArray.parseArray(dirData, WflowFormDir.class);
        } else {
            formDirs = formGroupService.getTree(null,processInstance.getProcessDefinitionId());
        }
        // 获取发起人信息
        String startUserName = "未知用户";
        String startUserDept = "";
        try {
            if (StrUtil.isNotBlank(processInstance.getStartUserId())) {
                startUserName = orgRepositoryService.getUserById(processInstance.getStartUserId()).getUserName();
                // 可以根据需要获取部门信息
            }
        } catch (Exception e) {
            log.warn("获取发起人信息失败: {}", processInstance.getStartUserId(), e);
        }

        return ProcessExportVo.ProcessBasicInfo.builder()
                .instanceId(instanceId)
                .processDefName(processInstance.getProcessDefinitionName())
                .instanceName(processInstance.getName())
                .processDefId(processInstance.getProcessDefinitionId())
                .deployId(processInstance.getDeploymentId())
                .version(processInstance.getProcessDefinitionVersion())
                .status(processInstance.getEndTime() != null ? "已完成" : "运行中")
                .startUserId(processInstance.getStartUserId())
                .startUserName(startUserName)
                .startUserDept(startUserDept)
                .startTime(processInstance.getStartTime() != null
                        ? processInstance.getStartTime().toInstant().atZone(ZoneId.systemDefault())
                        .toLocalDateTime()
                        : null)
                .endTime(processInstance.getEndTime() != null
                        ? processInstance.getEndTime().toInstant().atZone(ZoneId.systemDefault())
                        .toLocalDateTime()
                        : null)
                .description(processInstance.getDescription())
                .formData(JSONUtil.toJsonStr(formData))
                .formDir(JSONUtil.toJsonStr(formDirs))
                .build();
    }

    /**
     * 获取表单定义
     */
    private List<ProcessExportVo.FormDataInfo> getFormDefinitions(String instanceId) {
        List<ProcessExportVo.FormDataInfo> definitions = new ArrayList<>();

        try {
            // 通过流程实例获取表单ID
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(instanceId)
                    .singleResult();

            if (processInstance != null) {
                // 这里需要根据实际的表单定义存储方式来获取
                // 假设通过流程定义可以获取到表单配置
                String processDefId = processInstance.getProcessDefinitionId();
                // 查询表单模型
                List<WflowFormInfoHistorys> wflowFormInfoHistorys = varFormService.doForm(processDefId,null,null);
                definitions = wflowFormInfoHistorys.stream().map(map-> ProcessExportVo.FormDataInfo.builder()
                        .id(map.getHId())
                        .formConfig(map.getFormConfig())
                        .formItems(map.getFormItems())
                        .materialName(map.getMaterialName())
                        .build()).collect(Collectors.toList());

            }
        } catch (Exception e) {
            log.error("获取表单定义失败", e);
        }

        return definitions;
    }

    /**
     * 获取附件信息
     */
    private List<ProcessExportVo.AttachmentInfo> getAttachments(String instanceId) {
        List<ProcessExportVo.AttachmentInfo> attachments = new ArrayList<>();

        try {
           WflowFormData value = formDataMapper.selectOne(Wrappers.<WflowFormData>lambdaQuery().eq(WflowFormData::getInstanceId, instanceId)
                    .eq(WflowFormData::getFieldType,"FileUpload"));
            List<FileVo> fileVos = JSONArray.parseArray(value.getFieldValue(), FileVo.class);
            if (CollectionUtil.isNotEmpty(fileVos)) {
                for (FileVo fileVo : fileVos) {
                    attachments.add(ProcessExportVo.AttachmentInfo.builder()
                            .fileId(fileVo.getId())
                            .fileName(fileVo.getName())
                            .filePath(fileVo.getUrl())
                            .build()
                    );

                }
            }
        } catch (Exception e) {
            log.error("获取附件信息失败", e);
        }

        return attachments;
    }



    @Override
    public byte[] generateExcelFile(ProcessExportVo exportData) throws Exception {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // 1. 流程基本信息工作表
            excelHelper.createProcessInfoSheet(workbook, exportData.getProcessInfo(), headerStyle, dataStyle);
            // 2. 表单数据工作表
            excelHelper.createFormDataSheet(workbook, exportData.getFormData(), headerStyle, dataStyle);
            // 3. 附件清单工作表
            excelHelper.createAttachmentListSheet(workbook, exportData.getAttachments(), headerStyle, dataStyle);

            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("生成Excel文件失败", e);
            throw new RuntimeException("生成Excel文件失败", e);
        }
    }
    @Override
    public byte[] packageFiles(byte[] excelBytes, ProcessExportVo exportData) throws Exception {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zipOut = new ZipOutputStream(baos)) {

            // 添加Excel文件到ZIP
            String excelFileName = String.format("流程数据_%s.xlsx",
                    exportData.getProcessInfo().getInstanceName());
            ZipEntry excelEntry = new ZipEntry(excelFileName);
            zipOut.putNextEntry(excelEntry);
            zipOut.write(excelBytes);
            zipOut.closeEntry();

            // 添加附件文件到ZIP
            if (CollectionUtil.isNotEmpty(exportData.getAttachments())) {
                for (ProcessExportVo.AttachmentInfo attachment : exportData.getAttachments()) {
                    try (FileInputStream fis = new FileInputStream(getPathByOs(true,BASE_DIR_FILE+attachment.getFileId()))) {
                        ZipEntry attachmentEntry = new ZipEntry("file/"+attachment.getFileName());
                        zipOut.putNextEntry(attachmentEntry);
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = fis.read(buffer)) > 0) {
                            zipOut.write(buffer, 0, len);
                        }
                        zipOut.closeEntry();
                    }
                }
            }

            zipOut.finish();
            return baos.toByteArray();

        } catch (Exception e) {
            log.error("打包文件失败", e);
            throw new RuntimeException("打包文件失败", e);
        }
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);

        return style;
    }
    /**
     * @param isAb 是否相对路径
     * @param path xx.xx.xx
     * @return 对应系统下路径
     */
    public static synchronized String getPathByOs(boolean isAb, String ...path){
        return getString(isAb, OS, path);
    }
}
