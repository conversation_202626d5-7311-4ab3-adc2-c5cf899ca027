package com.wflow.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.GeometryFactory;
import com.vividsolutions.jts.geom.Point;
import com.wflow.bean.dto.CoordinateListVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.geotools.geometry.jts.JTS;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.geotools.referencing.CRS;
import org.geotools.referencing.GeodeticCalculator;
import org.opengis.referencing.FactoryException;
import org.opengis.referencing.crs.CoordinateReferenceSystem;
import org.opengis.referencing.operation.MathTransform;
import org.opengis.referencing.operation.TransformException;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExcelGeoJsonUtil {
    public static Object getGeoJson(String path,String zone) {
        JSONObject geoJsonObjectVo = new JSONObject();
        List<CoordinateListVo> coordinateListVos = new ArrayList<>(List.of());
        JSONObject geoJsonObject = new JSONObject();
        JSONObject feature, geometry;
        // feature 集合
        List<JSONObject> features = new ArrayList<>();
        // 放圈号集合
        List<List<List<Double>>> coordesFather = new ArrayList<>();
        // 放地块集合
        List<List<List<List<Double>>>> coordesSuper = new ArrayList<>();
        // 放坐标点集合
        List<List<Double>> coordes = new ArrayList<>();
        HashMap<String,Coordinate> coordinateHashMap = new HashMap<>();
        try (Workbook workbook = WorkbookFactory.create(new File(path))) {
            Sheet sheet = workbook.getSheetAt(0);
            // 创建实例
            feature = new JSONObject();
            feature.put("type", "Feature");
            geometry = new JSONObject();
            geometry.put("type", "MultiPolygon");
            // 构建属性信息
            Map<String, Object> properties = new HashMap<>();
            feature.put("properties", properties);
            feature.put("id", SnowflakeUtil.snowflakeId());
            // 从第2行开始读取（跳过标题行）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row currentRow = sheet.getRow(i);
                if (currentRow == null) {
                    continue;
                }
                // 按列索引读取数据
                String name = getCellValue(currentRow.getCell(0));
                String x = getCellValue(currentRow.getCell(1));
                String y = getCellValue(currentRow.getCell(2));
                String number = getCellValue(currentRow.getCell(4));
                String remark = getCellValue(currentRow.getCell(5));
                double longitude = Double.parseDouble(x);
                double latitude = Double.parseDouble(y);
                //判断y是否是37或者38开头
                BigDecimal bigDecimal = new BigDecimal(Double.toString(latitude));
                String plainString = bigDecimal.toPlainString();
                if (plainString.length()>=2 && StrUtil.isEmpty(zone)){
                    String substring = plainString.substring(0, 2);
                    if ("37".equals(substring)){
                        zone = "4525";
                    }else if ("38".equals(substring)){
                        zone = "4526";
                    }else {
                        zone = "4525";
                    }
                }
                // 创建投影坐标点
                Coordinate projectionCoordinate = new Coordinate(longitude, latitude);
                GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();
                Point geometryFactoryPoint = geometryFactory.createPoint(projectionCoordinate);

                // 定义投影坐标系和地理坐标系
                CoordinateReferenceSystem sourceCRSObject = CRS.decode("EPSG:" + zone);
                CoordinateReferenceSystem targetCRSObject = CRS.decode("EPSG:4490");

                // 创建坐标变换对象
                MathTransform transform = CRS.findMathTransform(sourceCRSObject, targetCRSObject, true);

                // 进行坐标转换
                Coordinate coordinate = JTS.transform(geometryFactoryPoint, transform).getCoordinate();

                ArrayList<Double> coord = Lists.newArrayList(coordinate.y, coordinate.x);
                String distance = "";
                // 计算距离
                if(!coordinateHashMap.isEmpty()){
                    GeodeticCalculator calculator = new GeodeticCalculator(targetCRSObject);
                    calculator.setStartingPosition(JTS.toDirectPosition(coordinateHashMap.get("1"),targetCRSObject));
                    calculator.setDestinationPosition(JTS.toDirectPosition(coordinate,targetCRSObject));
                    distance = String.valueOf(calculator.getOrthodromicDistance());
                }
                coordes.add(coord);
                coordinateListVos.add(CoordinateListVo.builder()
                        .name(name)
                        .x(x)
                        .y(y)
                        .length(distance)
                        .number(number)
                        .remark(remark)
                        .build());
                coordinateHashMap.put("1",coordinate);

            }
            coordesFather.add(new ArrayList<>(coordes));
            coordes.clear();
            coordesSuper.add(new ArrayList<>(coordesFather));
            coordesFather.clear();
            geometry.put("coordinates", new ArrayList<>(coordesSuper));
            coordesSuper.clear();
            feature.put("geometry", geometry);
            features.add(feature);
            geoJsonObject.put("type", "FeatureCollection");
            geoJsonObject.put("features", features);
            geoJsonObjectVo.put("features",geoJsonObject);
            geoJsonObjectVo.put("coordinateList",coordinateListVos);
        } catch (IOException | TransformException | FactoryException e) {
            throw new RuntimeException(e);
        }
        return geoJsonObjectVo;
    }
    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        return switch (cell.getCellType()) {
            case NUMERIC -> formatNumericValue(cell.getNumericCellValue());
            case STRING -> cell.getStringCellValue().trim();
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> handleFormulaCell(cell);
            default -> "";
        };
    }
    private static String formatNumericValue(double value) {
        // 处理整数
        if (value == Math.floor(value) && !Double.isInfinite(value)) {
            return String.valueOf((long) value);
        }

        // 处理小数
        BigDecimal bd = BigDecimal.valueOf(value);
        return bd.stripTrailingZeros().toPlainString();
    }

    private static String handleFormulaCell(Cell cell) {
        try {
            // 尝试获取公式的字符串结果
            return cell.getStringCellValue();
        } catch (IllegalStateException e) {
            // 如果是数值结果
            return formatNumericValue(cell.getNumericCellValue());
        }
    }
}
