package com.wflow;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;


/**
 * <AUTHOR> willian fu
 * @date : 2022/6/27
 */
@SpringBootApplication
@MapperScan(basePackages = {"com.wflow.mapper"})
@EnableKnife4j
public class AppStater {
    public static void main(String[] args) throws Exception  {
//        SpringApplication.run(AppStater.class, args);
        ConfigurableApplicationContext application = SpringApplication.run(AppStater.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("server.servlet.context-path");
        String path = property == null ? "" :  property;
        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "Application wflow is running! Access URLs:\n\t" +
//                        "Doc: \t\thttp://localhost:" + port + path + "/swagger-ui.html" + "\n\t" +
                        "Doc: \t\thttp://localhost:" + port + path + "/doc.html" + "\n\t" +
                        "Local: \t\thttp://localhost:" + port + path + "\n\t" +
                        "External: \thttp://" + ip + ":" + port + path + "\n\t" +
                        "----------------------------------------------------------");
    }
}
