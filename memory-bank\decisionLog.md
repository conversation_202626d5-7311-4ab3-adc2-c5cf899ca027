# Decision Log

This file records architectural and implementation decisions using a list format.
2025-05-07 10:39:36 - Log of updates made.

## Decision

* 采用Memory Bank模式管理项目知识

## Rationale

* 需要系统化记录架构决策和设计思路
* 便于团队成员共享项目上下文
* 符合复杂系统开发的最佳实践

## Implementation Details

* 创建5个核心Markdown文件
* 使用标准化模板和格式
* 自动记录时间戳
---
### 技术栈选择
[2025-05-07 10:41:16] - 确定核心技术栈

**Rationale:**
* 选择Spring Boot 2.2.3作为基础框架
* 采用Flowable 6.7.2作为工作流引擎
* 使用MyBatis-Plus简化数据访问层开发
* 集成Sa-Token处理权限认证

**Implications/Details:**
* 需要确保Flowable与Spring Boot版本兼容
* MyBatis-Plus需配置代码生成器
* Sa-Token需集成到Spring Security
---
### 部署策略选择
[2025-05-07 11:00:51] - 确定部署策略

**Rationale:**
* 选择 Docker 容器化部署，以方便迁移和扩展。

**Implications/Details:**
* 需要编写 Dockerfile
* 需要配置 Docker Compose
* 需要配置镜像仓库