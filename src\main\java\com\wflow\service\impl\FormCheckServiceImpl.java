package com.wflow.service.impl;

import com.wflow.bean.vo.ValidationResult;
import com.wflow.service.FormCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class FormCheckServiceImpl implements FormCheckService {
    @Override
    public Object checkForm(String formId) {
        List<ValidationResult> results = new ArrayList<>();

        return null;
    }
}
