package com.wflow.workflow.config;

import com.wflow.workflow.listener.JumpBackEventListener;
import com.wflow.workflow.service.JumpBackConfigService;
import com.wflow.workflow.service.JumpBackExecutionService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.spring.SpringProcessEngineConfiguration;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程回跳功能配置类
 * 配置事件监听器和相关组件
 *
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@Slf4j
@Configuration
public class JumpBackConfiguration {

    /**
     * 创建回跳事件监听器Bean
     * 使用 @Lazy 注解避免循环依赖
     */
    @Bean
    @Lazy
    public JumpBackEventListener jumpBackEventListener(@Lazy JumpBackConfigService jumpBackConfigService,
            @Lazy JumpBackExecutionService jumpBackExecutionService) {
        log.info("创建流程回跳事件监听器Bean");
        return new JumpBackEventListener(jumpBackConfigService, jumpBackExecutionService);
    }

    /**
     * 在应用启动完成后配置流程回跳事件监听器
     * 使用 ApplicationReadyEvent 确保所有Bean都已初始化完成
     */
    @EventListener(ApplicationReadyEvent.class)
    public void configureJumpBackEventListeners(ApplicationReadyEvent event) {
        log.info("应用启动完成，开始配置流程回跳事件监听器");

        try {
            ApplicationContext applicationContext = event.getApplicationContext();

            // 获取SpringProcessEngineConfiguration
            SpringProcessEngineConfiguration processEngineConfiguration = applicationContext
                    .getBean(SpringProcessEngineConfiguration.class);

            // 获取现有的事件监听器列表
            List<FlowableEventListener> eventListeners = processEngineConfiguration.getEventListeners();
            if (eventListeners == null) {
                eventListeners = new ArrayList<>();
            }

            // 从ApplicationContext获取回跳事件监听器Bean
            JumpBackEventListener listener = applicationContext.getBean(JumpBackEventListener.class);
            eventListeners.add(listener);

            // 设置回配置
            processEngineConfiguration.setEventListeners(eventListeners);

            log.info("流程回跳事件监听器配置完成，共注册 {} 个监听器", eventListeners.size());

        } catch (Exception e) {
            log.error("配置流程回跳事件监听器时发生异常: {}", e.getMessage(), e);
        }
    }
}
