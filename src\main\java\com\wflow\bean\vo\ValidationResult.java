package com.wflow.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResult {
    @Schema(name = "表单类型")
    private String fieldTitle;
    @Schema(name = "是否必填")
    private boolean isValid;
    @Schema(name = "错误信息")
    private String message;
}
