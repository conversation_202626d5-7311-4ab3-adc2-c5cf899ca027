package com.wflow.workflow.service;

import com.wflow.bean.entity.WflowModelHistorys;
import com.wflow.bean.vo.CustomPrintConfigVo;
import com.wflow.bean.vo.WflowModelHistorysVo;

/**
 * <AUTHOR> willian fu
 * @date : 2022/8/25
 */
public interface ProcessModelService {

    String saveProcess(WflowModelHistorys models);

    void enableProcess(String code, boolean enable);

    String deployProcess(String code);

    void delProcess(String code);

    WflowModelHistorys getLastVersionModel(String code);

    CustomPrintConfigVo getCustomPrintConfig(String instanceId);

    WflowModelHistorys getModelByDefId(String defId);

    //改造,新增根据流程id查询流程并查询wflow_node_form_bind表,根据表中节点和版本关系查询出各个节点表单
    WflowModelHistorys getModel(String code);
}
