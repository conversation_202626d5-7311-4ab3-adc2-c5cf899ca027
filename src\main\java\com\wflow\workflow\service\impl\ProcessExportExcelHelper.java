package com.wflow.workflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wflow.workflow.bean.vo.ProcessExportVo;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Excel导出辅助类
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Component
public class ProcessExportExcelHelper {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 创建流程基本信息工作表
     */
    public void createProcessInfoSheet(Workbook workbook, ProcessExportVo.ProcessBasicInfo processInfo,
            CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("流程基本信息");

        int rowNum = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = { "流程实例ID", "流程名称", "实例名称", "流程定义ID", "部署ID", "版本", "状态", "结果", "发起人ID", "发起人姓名", "发起人部门", "表单值", "表单目录", "流程进度", "发起时间", "结束时间", "描述" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        // 添加数据行
        if (ObjectUtil.isNotEmpty(processInfo)) {
            Row dataRow = sheet.createRow(1);
            setCellValue(dataRow, 0, processInfo.getInstanceId(), dataStyle);
            setCellValue(dataRow, 1, processInfo.getProcessDefName(), dataStyle);
            setCellValue(dataRow, 2, processInfo.getInstanceName(), dataStyle);
            setCellValue(dataRow, 3, processInfo.getProcessDefId(), dataStyle);
            setCellValue(dataRow, 4, processInfo.getDeployId(), dataStyle);
            setCellValue(dataRow, 5, String.valueOf(processInfo.getVersion()), dataStyle);
            setCellValue(dataRow, 6, processInfo.getStatus(), dataStyle);
            setCellValue(dataRow, 7, processInfo.getResult(), dataStyle);
            setCellValue(dataRow, 8, processInfo.getStartUserId(), dataStyle);
            setCellValue(dataRow, 9, processInfo.getStartUserName(), dataStyle);
            setCellValue(dataRow, 10, processInfo.getStartUserDept(), dataStyle);
            setCellValue(dataRow, 11, processInfo.getFormData(), dataStyle);
            setCellValue(dataRow, 12, processInfo.getFormDir(), dataStyle);
            setCellValue(dataRow, 13, processInfo.getProgress(), dataStyle);
            setCellValue(dataRow, 14, processInfo.getStartTime() != null ? processInfo.getStartTime().format(DATE_TIME_FORMATTER) : "", dataStyle);
            setCellValue(dataRow, 15, processInfo.getEndTime() != null ? processInfo.getEndTime().format(DATE_TIME_FORMATTER) : "", dataStyle);
            setCellValue(dataRow, 16, processInfo.getDescription(), dataStyle);
        }
        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建表单数据工作表
     */
    public void createFormDataSheet(Workbook workbook, List<ProcessExportVo.FormDataInfo> formData,
            CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("表单填写数据");

        int rowNum = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = { "id", "name", "formItems", "formConfig", "formDirId"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 添加数据行
        if (CollectionUtil.isNotEmpty(formData)) {
            for (ProcessExportVo.FormDataInfo data : formData) {
                Row dataRow = sheet.createRow(rowNum++);
                setCellValue(dataRow, 0, data.getId(), dataStyle);
                setCellValue(dataRow, 1, data.getMaterialName(), dataStyle);
                setCellValue(dataRow, 2, data.getFormItems(), dataStyle);
                setCellValue(dataRow, 3, data.getFormConfig(), dataStyle);
                setCellValue(dataRow, 4, data.getFormDirId(), dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }
    /**
     * 创建附件清单工作表
     */
    public void createAttachmentListSheet(Workbook workbook, List<ProcessExportVo.AttachmentInfo> attachments,
                                          CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("附件清单");

        int rowNum = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = { "id", "name", "type", "url"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 添加数据行
        if (CollectionUtil.isNotEmpty(attachments)) {
            for (ProcessExportVo.AttachmentInfo attachment : attachments) {
                Row dataRow = sheet.createRow(rowNum++);
                setCellValue(dataRow, 0, attachment.getFileId(), dataStyle);
                setCellValue(dataRow, 1, attachment.getFileName(), dataStyle);
                setCellValue(dataRow, 2, attachment.getFileType(), dataStyle);
                setCellValue(dataRow, 3, attachment.getFilePath(), dataStyle);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }
    /**
     * 添加数据行（两列格式）
     */
    private void addDataRow(Sheet sheet, int rowNum, String label, String value, CellStyle dataStyle) {
        Row row = sheet.createRow(rowNum);

        Cell labelCell = row.createCell(0);
        labelCell.setCellValue(label);
        labelCell.setCellStyle(dataStyle);

        Cell valueCell = row.createCell(1);
        valueCell.setCellValue(StrUtil.isNotBlank(value) ? value : "");
        valueCell.setCellStyle(dataStyle);
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(Row row, int columnIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(StrUtil.isNotBlank(value) ? value : "");
        cell.setCellStyle(cellStyle);
    }
}
