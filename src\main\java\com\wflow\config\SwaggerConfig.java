package com.wflow.config;


import org.springdoc.core.models.GroupedOpenApi;
import org.springdoc.core.properties.SwaggerUiConfigProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;



//@Configuration
public class SwaggerConfig {
//    // 创建默认分组包含所有API
//    @Bean
//    public GroupedOpenApi defaultApi() {
//        return GroupedOpenApi.builder()
//                .group("default")
//                .pathsToMatch("/**")
//                .build();
//    }
//
//    // 修复 swagger-config 端点问题
//    @Bean
//    public SwaggerUiConfigProperties swaggerUiConfig() {
//        SwaggerUiConfigProperties config = new SwaggerUiConfigProperties();
//        config.setConfigUrl("/v3/api-docs/swagger-config");
//        return config;
//    }
}
