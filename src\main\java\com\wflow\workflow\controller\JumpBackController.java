package com.wflow.workflow.controller;

import com.wflow.utils.R;
import com.wflow.workflow.bean.jumpback.JumpBackConfig;
import com.wflow.workflow.bean.jumpback.JumpBackResult;
import com.wflow.workflow.service.JumpBackConfigService;
import com.wflow.workflow.service.JumpBackExecutionService;
import io.swagger.v3.oas.annotations.Operation;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程回跳控制器
 * 提供回跳配置管理和回跳执行的API接口
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@Slf4j
@RestController
@RequestMapping("/wflow/jumpback")
@Tag(name = "流程回跳管理", description = "流程回跳配置和执行管理")
public class JumpBackController {

    @Autowired
    private JumpBackConfigService jumpBackConfigService;

    @Autowired
    private JumpBackExecutionService jumpBackExecutionService;

    /**
     * 创建回跳配置
     */
    @PostMapping("/config")
    @Operation(summary = "创建回跳配置", description = "为流程节点创建回跳配置")
    public Object createJumpBackConfig(@RequestBody JumpBackConfig config) {
        try {
            log.info("创建回跳配置: {}", config);

            // 验证配置
            JumpBackConfigService.ValidationResult validation = jumpBackConfigService.validateJumpBackConfig(config);
            if (!validation.isValid()) {
                return R.error("配置验证失败: " + validation.getErrorMessage());
            }

            String configId = jumpBackConfigService.saveJumpBackConfig(config);
            if (configId != null) {
                return R.success("创建回跳配置成功").put("configId", configId);
            } else {
                return R.error("创建回跳配置失败");
            }

        } catch (Exception e) {
            log.error("创建回跳配置时发生异常: {}", e.getMessage(), e);
            return R.error("创建回跳配置时发生异常: " + e.getMessage());
        }
    }

    /**
     * 更新回跳配置
     */
    @PutMapping("/config")
    @Operation(summary = "更新回跳配置", description = "更新现有的回跳配置")
    public Object updateJumpBackConfig(@RequestBody JumpBackConfig config) {
        try {
            log.info("更新回跳配置: {}", config.getId());

            // 验证配置
            JumpBackConfigService.ValidationResult validation = jumpBackConfigService.validateJumpBackConfig(config);
            if (!validation.isValid()) {
                return R.error("配置验证失败: " + validation.getErrorMessage());
            }

            boolean success = jumpBackConfigService.updateJumpBackConfig(config);
            return success ? R.ok("更新回跳配置成功") : R.error("更新回跳配置失败");

        } catch (Exception e) {
            log.error("更新回跳配置时发生异常: {}", e.getMessage(), e);
            return R.error("更新回跳配置时发生异常: " + e.getMessage());
        }
    }

    /**
     * 删除回跳配置
     */
    @DeleteMapping("/config/{configId}")
    @Operation(summary = "删除回跳配置", description = "删除指定的回跳配置")
    public Object deleteJumpBackConfig(@PathVariable String configId) {
        try {
            log.info("删除回跳配置: {}", configId);

            boolean success = jumpBackConfigService.deleteJumpBackConfig(configId);
            return success ? R.ok("删除回跳配置成功") : R.error("删除回跳配置失败");

        } catch (Exception e) {
            log.error("删除回跳配置时发生异常: {}", e.getMessage(), e);
            return R.error("删除回跳配置时发生异常: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用回跳配置
     */
    @PutMapping("/config/{configId}/toggle")
    @Operation(summary = "启用/禁用回跳配置", description = "切换回跳配置的启用状态")
    public Object toggleJumpBackConfig(@PathVariable String configId,
            @RequestParam boolean enabled) {
        try {
            log.info("切换回跳配置状态: {} -> {}", configId, enabled);

            boolean success = jumpBackConfigService.toggleJumpBackConfig(configId, enabled);
            String action = enabled ? "启用" : "禁用";
            return success ? R.ok(action + "回跳配置成功") : R.error(action + "回跳配置失败");

        } catch (Exception e) {
            log.error("切换回跳配置状态时发生异常: {}", e.getMessage(), e);
            return R.error("切换回跳配置状态时发生异常: " + e.getMessage());
        }
    }

    /**
     * 获取流程的回跳配置列表
     */
    @GetMapping("/config/process/{processDefId}")
    @Operation(summary = "获取流程回跳配置", description = "获取指定流程定义的所有回跳配置")
    public Object getJumpBackConfigs(@PathVariable String processDefId) {
        try {
            log.debug("获取流程回跳配置: {}", processDefId);

            List<JumpBackConfig> configs = jumpBackConfigService.getJumpBackConfigsByProcessDef(processDefId);
            return R.ok(configs);

        } catch (Exception e) {
            log.error("获取流程回跳配置时发生异常: {}", e.getMessage(), e);
            return R.error("获取流程回跳配置时发生异常: " + e.getMessage());
        }
    }

    /**
     * 解析流程定义中的回跳配置
     */
    @PostMapping("/config/parse/{processDefId}")
    @Operation(summary = "解析流程回跳配置", description = "从流程定义中解析回跳配置")
    public Object parseJumpBackConfigs(@PathVariable String processDefId) {
        try {
            log.info("解析流程定义回跳配置: {}", processDefId);

            List<JumpBackConfig> configs = jumpBackConfigService.parseJumpBackConfigs(processDefId);
            return R.success("解析完成").put("configs", configs).put("count", configs.size());

        } catch (Exception e) {
            log.error("解析流程回跳配置时发生异常: {}", e.getMessage(), e);
            return R.error("解析流程回跳配置时发生异常: " + e.getMessage());
        }
    }

    /**
     * 检查回跳条件
     */
    @GetMapping("/check/{processInstanceId}")
    @Operation(summary = "检查回跳条件", description = "检查流程实例是否满足回跳条件")
    public Object checkJumpBackCondition(@PathVariable String processInstanceId,
            @RequestParam String nodeId) {
        try {
            log.debug("检查回跳条件: 流程={}, 节点={}", processInstanceId, nodeId);

            boolean canJumpBack = jumpBackExecutionService.checkJumpBackCondition(processInstanceId, nodeId);
            return R.success("检查完成").put("canJumpBack", canJumpBack);

        } catch (Exception e) {
            log.error("检查回跳条件时发生异常: {}", e.getMessage(), e);
            return R.error("检查回跳条件时发生异常: " + e.getMessage());
        }
    }

    /**
     * 执行回跳操作
     */
    @PostMapping("/execute/{processInstanceId}")
    @Operation(summary = "执行回跳操作", description = "执行流程实例的回跳操作")
    public Object executeJumpBack(@PathVariable String processInstanceId,
            @RequestParam String configId,
            @RequestParam(required = false) String reason) {
        try {
            log.info("执行回跳操作: 流程={}, 配置={}, 原因={}", processInstanceId, configId, reason);

            // 检查是否可以回跳
            if (!jumpBackExecutionService.canJumpBack(processInstanceId, configId)) {
                return R.error("当前不满足回跳条件");
            }

            JumpBackResult result = jumpBackExecutionService.executeJumpBack(processInstanceId, configId, reason);

            if (result.getSuccess()) {
                return R.success("回跳执行成功").put("result", result);
            } else {
                return R.error("回跳执行失败: " + result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("执行回跳操作时发生异常: {}", e.getMessage(), e);
            return R.error("执行回跳操作时发生异常: " + e.getMessage());
        }
    }

    /**
     * 获取回跳历史
     */
    @GetMapping("/history/{processInstanceId}")
    @Operation(summary = "获取回跳历史", description = "获取流程实例的回跳历史记录")
    public Object getJumpBackHistory(@PathVariable String processInstanceId) {
        try {
            log.debug("获取回跳历史: {}", processInstanceId);

            List<JumpBackResult> history = jumpBackExecutionService.getJumpBackHistory(processInstanceId);
            return R.ok(history);

        } catch (Exception e) {
            log.error("获取回跳历史时发生异常: {}", e.getMessage(), e);
            return R.error("获取回跳历史时发生异常: " + e.getMessage());
        }
    }

    /**
     * 取消待执行的回跳
     */
    @PostMapping("/cancel/{processInstanceId}")
    @Operation(summary = "取消回跳操作", description = "取消待执行的回跳操作")
    public Object cancelJumpBack(@PathVariable String processInstanceId,
            @RequestParam String configId) {
        try {
            log.info("取消回跳操作: 流程={}, 配置={}", processInstanceId, configId);

            boolean success = jumpBackExecutionService.cancelPendingJumpBack(processInstanceId, configId);
            return success ? R.ok("取消回跳操作成功") : R.error("取消回跳操作失败");

        } catch (Exception e) {
            log.error("取消回跳操作时发生异常: {}", e.getMessage(), e);
            return R.error("取消回跳操作时发生异常: " + e.getMessage());
        }
    }

    /**
     * 重置回跳状态
     */
    @PostMapping("/reset/{processInstanceId}")
    @Operation(summary = "重置回跳状态", description = "重置流程实例的回跳状态")
    public Object resetJumpBackState(@PathVariable String processInstanceId,
            @RequestParam String configId) {
        try {
            log.info("重置回跳状态: 流程={}, 配置={}", processInstanceId, configId);

            boolean success = jumpBackExecutionService.resetJumpBackState(processInstanceId, configId);
            return success ? R.ok("重置回跳状态成功") : R.error("重置回跳状态失败");

        } catch (Exception e) {
            log.error("重置回跳状态时发生异常: {}", e.getMessage(), e);
            return R.error("重置回跳状态时发生异常: " + e.getMessage());
        }
    }

    /**
     * 获取回跳状态信息
     */
    @GetMapping("/status/{processInstanceId}")
    @Operation(summary = "获取回跳状态", description = "获取流程实例的回跳状态信息")
    public Object getJumpBackStatus(@PathVariable String processInstanceId,
            @RequestParam String configId) {
        try {
            log.debug("获取回跳状态: 流程={}, 配置={}", processInstanceId, configId);

            boolean canJumpBack = jumpBackExecutionService.canJumpBack(processInstanceId, configId);
            int currentJumpCount = jumpBackExecutionService.getCurrentJumpCount(processInstanceId, configId);

            return R.success("获取回跳状态成功")
                    .put("canJumpBack", canJumpBack)
                    .put("currentJumpCount", currentJumpCount);

        } catch (Exception e) {
            log.error("获取回跳状态时发生异常: {}", e.getMessage(), e);
            return R.error("获取回跳状态时发生异常: " + e.getMessage());
        }
    }
}
