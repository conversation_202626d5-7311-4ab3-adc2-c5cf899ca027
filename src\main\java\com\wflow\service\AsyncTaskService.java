package com.wflow.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.AsyncTask;
import com.wflow.bean.enums.TaskStatus;
import com.wflow.bean.vo.AsyncTaskVo;
import com.wflow.bean.vo.LandAcquisitionStatisticsVo;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 异步任务管理Service
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
public interface AsyncTaskService {

        /**
         * 创建异步任务
         */
        AsyncTask createTask(String taskType, String taskName, String parameters,
                        Integer priority, Integer estimatedTime, String userId);

        /**
         * 提交异步任务
         */
        AsyncTaskVo submitTask(String taskType, String taskName, String parameters,
                        Integer priority, Integer estimatedTime, String userId);

        /**
         * 获取任务状态
         */
        AsyncTaskVo getTaskStatus(String taskId);

        /**
         * 获取任务详情
         */
        AsyncTask getTaskById(String taskId);

        /**
         * 更新任务状态
         */
        boolean updateTaskStatus(String taskId, TaskStatus status, Integer progress, String errorMessage);

        /**
         * 更新任务进度
         */
        boolean updateTaskProgress(String taskId, Integer progress);

        /**
         * 完成任务
         */
        boolean completeTask(String taskId, Object result);

        /**
         * 失败任务
         */
        boolean failTask(String taskId, String errorMessage);

        /**
         * 取消任务
         */
        boolean cancelTask(String taskId);

        /**
         * 获取待处理任务列表
         */
        List<AsyncTask> getPendingTasks(int limit);

        /**
         * 获取用户任务列表
         */
        List<AsyncTask> getUserTasks(String userId, int limit);

        /**
         * 清理过期任务
         */
        int cleanupExpiredTasks();

        /**
         * 重试失败任务
         */
        boolean retryTask(String taskId);

        /**
         * 异步执行征地统计任务
         */
        CompletableFuture<Page<LandAcquisitionStatisticsVo>> executeLandStatisticsTask(String taskId, String xzqCode,
                        Map<String, Object> filters, String formName, String configName,
                        Integer pageNo, Integer pageSize, String sortField, String sortOrder);

        /**
         * 估算任务处理时间
         */
        Integer estimateTaskTime(String taskType, String parameters);

        /**
         * 判断是否需要异步处理
         */
        boolean shouldUseAsync(String xzqCode, String taskType);

        /**
         * 获取任务统计信息
         */
        Object getTaskStatistics();

        /**
         * 获取征地统计任务结果（类型安全）
         */
        Page<LandAcquisitionStatisticsVo> getLandStatisticsTaskResult(String taskId);

        /**
         * 等待任务完成并获取结果
         * 
         * @param taskId         任务ID
         * @param timeoutSeconds 超时时间（秒）
         * @return 任务结果，如果超时或失败返回null
         */
        Object waitForTaskCompletion(String taskId, int timeoutSeconds);
}
