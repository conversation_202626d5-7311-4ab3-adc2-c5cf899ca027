package com.wflow.config.filter;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.regex.Pattern;


/**
 * @Author：qinyi
 * @Date：2025-1-7 09:55
 */
public class TokenInterceptor implements HandlerInterceptor {
    private static final Pattern ALLOWED_URI_PATTERN = Pattern.compile(
            "^(/sys/auth/login/cross-system)$|" +
                    ".*(swagger-ui|swagger-resources|/v3/api-docs|wflow/res|/doc.html|/webjars|/favicon.ico|/swagger-resources).*"
    );
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 从HTTP头信息中取得token
        String token = request.getHeader("Wflowtoken");

        // 获取请求路径
        String requestURI = request.getRequestURI();
        if (ALLOWED_URI_PATTERN.matcher(requestURI).matches()) {
            return true;
        }
            if (StringUtils.isBlank(token)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return false;
        }
        return true;
    }

}
