package com.wflow.config;


import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.io.IOException;
import java.util.Collections;

/**
 * <AUTHOR> willian fu
 * @version : 1.0
 * 设置跨域
 */
@Configuration
public class GlobalCorsConfig {//implements Filter {// extends WebMvcConfigurerAdapter {

    @Bean
    public FilterRegistrationBean filterRegistrationBean() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
//        //1.允许任何来源
//        corsConfiguration.addAllowedOrigin("*");
        //2.允许任何请求头
        corsConfiguration.addAllowedHeader(CorsConfiguration.ALL);
        //3.允许任何方法
        corsConfiguration.addAllowedMethod(CorsConfiguration.ALL);
        //4.允许凭证
        corsConfiguration.setAllowCredentials(true);
        //允许域名使用
        corsConfiguration.addAllowedOriginPattern("*");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);
        CorsFilter corsFilter = new CorsFilter(source);

        FilterRegistrationBean<CorsFilter> filterRegistrationBean=new FilterRegistrationBean<>(corsFilter);
        filterRegistrationBean.setOrder(-101);
        return filterRegistrationBean;
    }

    //@Override
// 标识该方法重写了父类或接口中的方法
//    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
//    // 将ServletResponse转换为HttpServletResponse类型，以便添加HTTP响应头
//        HttpServletResponse res = (HttpServletResponse) response;
//    // 添加HTTP响应头，允许跨域请求携带凭证（如Cookies）
//        res.addHeader("Access-Control-Allow-Credentials", "true");
//    // 添加HTTP响应头，允许所有来源的跨域请求
//        res.addHeader("Access-Control-Allow-Origin", "*");
//    // 添加HTTP响应头，允许所有类型的HTTP请求头
//        res.addHeader("Access-Control-Allow-Headers", "*");
//    // 添加HTTP响应头，允许所有HTTP请求方法（注释掉的代码）
//        //res.addHeader("Access-Control-Allow-Methods", "*");
//    // 添加HTTP响应头，明确指定允许的HTTP请求方法
//        res.addHeader("Access-Control-Allow-Methods", "GET, POST, DELETE, PUT, OPTIONS, PATCH, HEAD");
//    // 检查请求方法是否为OPTIONS（预检请求）
//        if (((HttpServletRequest) request).getMethod().equals("OPTIONS")) {
//        // 如果是OPTIONS请求，返回一个简单的响应"ok"
//            response.getWriter().println("ok");
//        // 结束方法执行，不再继续调用后续的过滤器
//            return;
//        }
//    // 调用过滤器链的下一个过滤器，继续处理请求和响应
//        chain.doFilter(request, response);
//    }

    /*@Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowCredentials(true)
                .allowedMethods("*")
                .allowedOrigins("*")
                .allowedHeaders("*")
                .maxAge(3600 * 5);
    }*/
}
