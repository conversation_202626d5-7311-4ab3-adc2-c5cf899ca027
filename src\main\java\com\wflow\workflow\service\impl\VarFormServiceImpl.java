package com.wflow.workflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wflow.bean.entity.WflowFormInfo;
import com.wflow.bean.entity.WflowFormInfoHistorys;
import com.wflow.bean.entity.WflowFormRecord;
import com.wflow.bean.entity.WflowNodeFormInfo;
import com.wflow.mapper.WflowFormInfoHistorysMapper;
import com.wflow.mapper.WflowFormInfoMapper;
import com.wflow.mapper.WflowModelsMapper;
import com.wflow.mapper.WflowNodeFormInfoMapper;
import com.wflow.workflow.bean.process.ProcessNode;
import com.wflow.workflow.bean.process.form.Form;
import com.wflow.workflow.bean.process.form.FormPerm;
import com.wflow.workflow.bean.process.props.ApprovalProps;
import com.wflow.workflow.bean.process.props.CcProps;
import com.wflow.workflow.bean.process.props.RootProps;
import com.wflow.workflow.bean.process.props.SubProcessProps;
import com.wflow.workflow.bean.vo.FormDataChangeLogVo;
import com.wflow.workflow.service.FormService;
import com.wflow.workflow.service.ProcessNodeCatchService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 从流程变量存取表单数据
 *
 * <AUTHOR> willian fu
 * @date : 2022/8/24
 */
@Primary
@Service("varFormService")
@Slf4j
public class VarFormServiceImpl extends AbstractFormServiceImpl implements FormService {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private WflowNodeFormInfoMapper nodeFormInfoMapper;

    @Autowired
    private WflowFormInfoMapper formInfoMapper;
    @Autowired
    private WflowFormInfoHistorysMapper formInfoHistorysMapper;

    @Override
    public Map<String, Object> getProcessInstanceFormData(String instanceId) {
        try {
            return historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(instanceId).variableNameLike("field%").list()
                    .stream().collect(Collectors.toMap(
                            HistoricVariableInstance::getVariableName,
                            HistoricVariableInstance::getValue)
                    );
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getFormFieldData(String instanceId, Collection<String> fieldIds) {
        Map<String, Object> formData;
        //先从运行时变量中获取,没有再从历史变量中获取
        try {
            formData = runtimeService.getVariables(instanceId, fieldIds);
        } catch (Exception e) {
            formData = historyService.createNativeHistoricVariableInstanceQuery()
                    .sql(StrUtil.builder().append("select * from ACT_HI_VARINST where 9 = #{instanceId} and NAME_ in ('")
                            .append(String.join("','", fieldIds)).append("')").toString())
                    .parameter("instanceId", instanceId)
                    .list()
                    .stream().collect(Collectors.toMap(
                            HistoricVariableInstance::getVariableName,
                            HistoricVariableInstance::getValue)
                    );
        }
        return formData;
    }

    @Override
    public Map<String, Map<String, Object>> getFormFieldDataBatch(Map<String, ? extends Collection<String>> instanceFieldMap) {
        //实例id -> (字段id -> 字段值)
        Map<String, Map<String, Object>> instanceDatas = new HashMap<>(instanceFieldMap.size());
        //TODO 暂时循环查，对于流程变量里面查目前没想到更好的办法
            instanceFieldMap.forEach((instanceId, fieldIds) -> {
                instanceDatas.put(instanceId, getFormFieldData(instanceId, fieldIds));
            });
        return instanceDatas;
    }

    @Override
    public void saveInstanceFormData(String instanceId, Map<String, Object> formData) {
        //runtimeService.setVariables(instanceId, formData);
    }

    @Override
    public List<WflowFormRecord> updateInstanceFormData(String userId, String instanceId, Map<String, Object> formData) {
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, Object> variables = runtimeService.getVariables(instanceId, formData.keySet());
        Date time = GregorianCalendar.getInstance().getTime();
        List<WflowFormRecord> records = new ArrayList<>(formData.size());
        formData.forEach((k, newVal) -> {
            Object oldVal = variables.get(k);
            //做下比较，不同就加入修改
            if (isDiff(oldVal, newVal)) {
                dataMap.put(k, newVal);
                records.add(WflowFormRecord.builder()
                        .updateBy(userId).id(IdUtil.objectId())
                        .createTime(time)
                        .fieldId(k).instanceId(instanceId)
                        .oldValue(coverToString(oldVal))
                        .newValue(coverToString(newVal)).build());
            }
        });
        if (CollectionUtil.isNotEmpty(dataMap)) {
            runtimeService.setVariables(instanceId, dataMap);
        }
        return records;
    }

    @Override
    public Map<String, Object> getProcessNodeFormData(String instanceId, String nodeId) {
        try {
            Map<String,Object> map = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(instanceId).variableName("field%").list()
                    .stream().collect(Collectors.toMap(
                            HistoricVariableInstance::getVariableName,
                            HistoricVariableInstance::getValue)
                    );

            List<WflowFormInfoHistorys> formInfos = doForm(instanceId, nodeId,null);
            //处理数据
            List<Map<String, Form>> info = formInfos.stream().map(v->{
                Map<String, Form> formMap;
                formMap = loadFormItemsMap(v.getFormItems());
                return formMap;
            }).collect(Collectors.toList());
            //收集所有有效 key
            Set<String> validKeys = info.stream()
                    .filter(Objects::nonNull)
                    .flatMap(v -> v.keySet().stream())
                    .collect(Collectors.toSet());
            //生成新Map
            return map.entrySet().stream()
                    .filter(entry -> validKeys.contains(entry.getKey()))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue
                    ));
        } catch (Exception e) {
            return new HashMap<>();
        }
    }


    public List<WflowFormInfoHistorys> doForm(String instanceId, String nodeId,String modelHistorysId) {
        // 查询关联的 formInfoId
        List<WflowNodeFormInfo> wflowNodeFormInfoList = nodeFormInfoMapper.selectList(
                Wrappers.<WflowNodeFormInfo>lambdaQuery()
                        .select(WflowNodeFormInfo::getFormInfoId)
                        .eq(StringUtils.isNotBlank(instanceId),WflowNodeFormInfo::getProcessDefId, instanceId)
                        .eq(StringUtils.isNotBlank(modelHistorysId),WflowNodeFormInfo::getModelHistorysId, modelHistorysId)
                        .eq(StringUtils.isNotBlank(nodeId),WflowNodeFormInfo::getNodeId, nodeId)
        );

        // 提取 formInfoId 的值集合
        List<String> formInfoIds = wflowNodeFormInfoList.stream()
                .map(WflowNodeFormInfo::getFormInfoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 根据 formInfoIds 查询表单信息
        List<WflowFormInfoHistorys> formInfos = Collections.emptyList();
        if (!formInfoIds.isEmpty()) {
            formInfos = formInfoHistorysMapper.selectList(
                    Wrappers.<WflowFormInfoHistorys>lambdaQuery()
                            .in(WflowFormInfoHistorys::getId, formInfoIds)
            );
        } else {
            log.warn("未找到关联的表单ID，instanceId={}, nodeId={}", instanceId, nodeId);
        }

        return formInfos;
    }
}
