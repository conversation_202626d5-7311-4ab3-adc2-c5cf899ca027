package com.wflow.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wflow.bean.entity.WflowFormData;
import com.wflow.mapper.WflowFormDataMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单数据过滤器工具类
 * 用于根据WflowFormData表中的字段值进行过滤
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
public class FormDataFilterUtils {

    /**
     * 构建WflowFormData的查询条件，用于过滤表单数据
     * 
     * @param filters 过滤条件Map，key为fieldId，value为要匹配的fieldValue
     * @return 包含过滤条件的实例ID集合，如果没有过滤条件则返回null
     */
    public static Set<String> buildFormDataFilterConditions(Map<String, Object> filters) {

        if (filters == null || filters.isEmpty()) {
            log.debug("没有过滤条件，跳过表单数据过滤");
            return null;
        }

        log.debug("开始构建表单数据过滤条件，过滤字段数: {}", filters.size());

        // 存储每个过滤条件匹配的实例ID集合
        List<Set<String>> filterResults = new ArrayList<>();

        for (Map.Entry<String, Object> filter : filters.entrySet()) {
            String fieldId = filter.getKey();
            Object fieldValue = filter.getValue();

            if (fieldValue == null) {
                log.debug("跳过空值过滤条件，字段ID: {}", fieldId);
                continue;
            }

            Set<String> matchingInstanceIds = getInstanceIdsByFieldFilter(fieldId, fieldValue.toString());
            filterResults.add(matchingInstanceIds);

            log.debug("字段过滤结果 - 字段ID: {}, 字段值: {}, 匹配实例数: {}",
                    fieldId, fieldValue, matchingInstanceIds.size());
        }

        if (filterResults.isEmpty()) {
            log.debug("所有过滤条件都为空，跳过过滤");
            return null;
        }

        // 计算所有过滤条件的交集（AND逻辑）
        Set<String> finalInstanceIds = filterResults.get(0);
        for (int i = 1; i < filterResults.size(); i++) {
            finalInstanceIds = finalInstanceIds.stream()
                    .filter(filterResults.get(i)::contains)
                    .collect(Collectors.toSet());
        }

        log.debug("过滤条件AND运算结果，最终匹配实例数: {}", finalInstanceIds.size());

        return finalInstanceIds;
    }

    /**
     * 根据字段ID和字段值查询匹配的实例ID集合
     * @param fieldId 字段ID
     * @param fieldValue 字段值
     * @return 匹配的实例ID集合
     */
    private static Set<String> getInstanceIdsByFieldFilter(String fieldId, String fieldValue) {
        try {
            // 通过BeanUtil获取WflowFormDataMapper实例
            WflowFormDataMapper formDataMapper = BeanUtil.getBean(WflowFormDataMapper.class);
            
            LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WflowFormData::getFieldId, fieldId)
                   .eq(WflowFormData::getFieldValue, fieldValue);
            
            List<WflowFormData> matchingData = formDataMapper.selectList(wrapper);
            
            return matchingData.stream()
                    .map(WflowFormData::getInstanceId)
                    .collect(Collectors.toSet());
                    
        } catch (Exception e) {
            log.error("查询字段过滤数据失败，字段ID: {}, 字段值: {}", fieldId, fieldValue, e);
            return new HashSet<>();
        }
    }

    /**
     * 应用实例ID过滤条件到表单数据查询
     * @param formDataList 原始表单数据列表
     * @param filteredInstanceIds 过滤后的实例ID集合
     * @return 过滤后的表单数据列表
     */
    public static List<WflowFormData> applyInstanceIdFilter(List<WflowFormData> formDataList, Set<String> filteredInstanceIds) {
        if (filteredInstanceIds == null || filteredInstanceIds.isEmpty()) {
            return formDataList;
        }
        
        return formDataList.stream()
                .filter(data -> filteredInstanceIds.contains(data.getInstanceId()))
                .collect(Collectors.toList());
    }

    /**
     * 构建包含实例ID过滤条件的查询包装器
     * @param baseWrapper 基础查询条件
     * @param filteredInstanceIds 过滤后的实例ID集合
     * @return 包含过滤条件的查询包装器
     */
    public static LambdaQueryWrapper<WflowFormData> applyInstanceIdFilterToWrapper(
            LambdaQueryWrapper<WflowFormData> baseWrapper, Set<String> filteredInstanceIds) {
        
        if (filteredInstanceIds != null && !filteredInstanceIds.isEmpty()) {
            baseWrapper.in(WflowFormData::getInstanceId, filteredInstanceIds);
        }
        
        return baseWrapper;
    }

    /**
     * 从filters中提取特定字段的值
     * @param filters 过滤条件Map
     * @param fieldName 字段名
     * @param defaultValue 默认值
     * @return 字段值
     */
    public static <T> T getFilterValue(Map<String, Object> filters, String fieldName, T defaultValue) {
        if (filters == null || !filters.containsKey(fieldName)) {
            return defaultValue;
        }
        
        Object value = filters.get(fieldName);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            @SuppressWarnings("unchecked")
            T result = (T) value;
            return result;
        } catch (ClassCastException e) {
            log.warn("类型转换失败，字段: {}, 值: {}, 期望类型: {}", 
                    fieldName, value, defaultValue != null ? defaultValue.getClass().getSimpleName() : "null");
            return defaultValue;
        }
    }

    /**
     * 检查是否包含传统的过滤参数（isCompleted, landStatusFieldId）
     * @param filters 过滤条件Map
     * @return 是否包含传统参数
     */
    public static boolean containsLegacyFilters(Map<String, Object> filters) {
        if (filters == null || filters.isEmpty()) {
            return false;
        }
        
        return filters.containsKey("isCompleted") || filters.containsKey("landStatusFieldId");
    }

    /**
     * 从filters中提取传统的过滤参数
     * @param filters 过滤条件Map
     * @return 包含isCompleted和landStatusFieldId的Map
     */
    public static Map<String, Object> extractLegacyFilters(Map<String, Object> filters) {
        Map<String, Object> legacyFilters = new HashMap<>();
        
        if (filters != null) {
            if (filters.containsKey("isCompleted")) {
                legacyFilters.put("isCompleted", filters.get("isCompleted"));
            }
            if (filters.containsKey("landStatusFieldId")) {
                legacyFilters.put("landStatusFieldId", filters.get("landStatusFieldId"));
            }
        }
        
        return legacyFilters;
    }

    /**
     * 从filters中提取自定义字段过滤条件（排除传统参数）
     * @param filters 过滤条件Map
     * @return 不包含isCompleted和landStatusFieldId的Map
     */
    public static Map<String, Object> extractCustomFilters(Map<String, Object> filters) {
        if (filters == null || filters.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<String, Object> customFilters = new HashMap<>(filters);
        customFilters.remove("isCompleted");
        customFilters.remove("landStatusFieldId");
        
        return customFilters;
    }

    /**
     * 验证过滤条件的格式
     * @param filters 过滤条件Map
     * @return 验证结果，包含错误信息
     */
    public static List<String> validateFilters(Map<String, Object> filters) {
        List<String> errors = new ArrayList<>();
        
        if (filters == null) {
            return errors; // null是有效的（表示无过滤条件）
        }
        
        for (Map.Entry<String, Object> entry : filters.entrySet()) {
            String fieldId = entry.getKey();
            Object fieldValue = entry.getValue();
            
            // 验证字段ID格式
            if (fieldId == null || fieldId.trim().isEmpty()) {
                errors.add("字段ID不能为空");
                continue;
            }
            
            // 验证字段值
            if (fieldValue == null) {
                log.debug("字段 {} 的值为null，将被跳过", fieldId);
                continue;
            }
            
            // 验证字段ID格式（应该是field开头的字符串）
            if (!fieldId.equals("isCompleted") && !fieldId.equals("landStatusFieldId") && 
                !fieldId.startsWith("field")) {
                errors.add("字段ID格式不正确: " + fieldId + "，应该以'field'开头或为传统参数");
            }
        }
        
        return errors;
    }

    /**
     * 记录过滤操作的统计信息
     * @param filters 过滤条件
     * @param originalCount 原始记录数
     * @param filteredCount 过滤后记录数
     */
    public static void logFilterStatistics(Map<String, Object> filters, int originalCount, int filteredCount) {
        if (filters == null || filters.isEmpty()) {
            log.debug("无过滤条件，记录数保持不变: {}", originalCount);
            return;
        }
        
        double filterRate = originalCount > 0 ? (double) filteredCount / originalCount * 100 : 0;
        
        log.info("过滤统计 - 过滤条件数: {}, 原始记录数: {}, 过滤后记录数: {}, 保留率: {:.2f}%", 
                filters.size(), originalCount, filteredCount, filterRate);
        
        // 记录具体的过滤条件
        for (Map.Entry<String, Object> entry : filters.entrySet()) {
            log.debug("  过滤条件: {} = {}", entry.getKey(), entry.getValue());
        }
    }
}
