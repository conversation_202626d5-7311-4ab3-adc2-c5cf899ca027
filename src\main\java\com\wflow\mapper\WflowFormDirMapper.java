package com.wflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wflow.bean.entity.WflowFormDir;
import com.wflow.bean.entity.WflowFormInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> qinyi
 * @date : 2024/11/21
 */
public interface WflowFormDirMapper extends BaseMapper<WflowFormDir> {

    @Select("WITH RECURSIVE dir as (SELECT * FROM wflow_form_dir WHERE id=#{formDirId} UNION ALL SELECT wflow_form_dir.* from wflow_form_dir INNER JOIN dir ON wflow_form_dir.parent_id = dir.id)  select * from dir")
    List<String> selectSelfAndChildIds(@Param("formDirId") String formDirId);

    @Select({
            "<script>",
            "WITH RECURSIVE cte AS (",
            "SELECT ",
            "  id, dir_name, show_name, parent_id, show_sort, ",
            "  create_time, update_time, is_expand, 0 AS level ",
            "FROM wflow_form_dir ",
            "WHERE id IN ",
            "  <foreach item='id' collection='ids' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",
            "UNION ",
            "SELECT ",
            "  parent.id, parent.dir_name, parent.show_name, parent.parent_id, ",
            "  parent.show_sort, parent.create_time, parent.update_time, ",
            "  parent.is_expand, cte.level + 1 ",
            "FROM wflow_form_dir parent ",
            "INNER JOIN cte ON parent.id = cte.parent_id ",
            "WHERE parent.id != '0'",
            ") ",
            "SELECT DISTINCT * FROM cte ",
            "ORDER BY level DESC",
            "</script>"
    })
    List<WflowFormDir> selectParentTreeByIds(@Param("ids") List<String> ids);

}
