package com.wflow.service;

import com.wflow.bean.entity.WflowFormDir;
import com.wflow.bean.entity.WflowFormInfo;
import com.wflow.bean.vo.NodeFormVo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> qinyi
 * @date : 2024/11/29
 */
public interface ModelHistorysFormIdService {

    Object relationFormDir(String modelHistorysId, String formDirIds);

    @Transactional
    Object relationNodeFormInfo(List<NodeFormVo> nodeFormVo);

    Object removeRelationNodeFormInfo(String nodeId, String formInfoIds);

    Object removeRelationFormDir(String modelHistorysId, String formDirIds);

    Object getNodeFormInfoList(String nodeId);

    Object copyFormInfoByNodeId(String formNodeId, String toNodeId, String toNodeName);

    Object getFormInfoByRelationFormDir(String modelHistorysId);

    Object getRelationFormDirList(String modelHistorysId);
}
