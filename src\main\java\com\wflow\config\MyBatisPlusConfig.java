package com.wflow.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.wflow.workflow.config.WflowGlobalVarDef;
import org.apache.ibatis.logging.stdout.StdOutImpl;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> willian fu
 * @date : 2022/9/20
 */
@Configuration
public class MyBatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor(DataSourceProperties dataSourceProperties){
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        //添加分页插件
        if (dataSourceProperties.getDriverClassName().contains(DbType.POSTGRE_SQL.getDb())) {
            WflowGlobalVarDef.DB_TYPE = DbType.POSTGRE_SQL;
            interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
        } else if (dataSourceProperties.getDriverClassName().contains(DbType.ORACLE.getDb())) {
            WflowGlobalVarDef.DB_TYPE = DbType.ORACLE;
//            ORACLE("oracle", "Oracle11g及以下数据库(高版本推荐使用ORACLE_NEW)"),
//            ORACLE_12C("oracle12c", "Oracle12c+数据库"),
            interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.ORACLE));
        }
        return interceptor;
    }
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            configuration.setLogPrefix("MYBATIS_DEBUG:");
            configuration.setLogImpl(StdOutImpl.class);
        };
    }
}
