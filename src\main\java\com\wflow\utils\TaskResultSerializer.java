package com.wflow.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.vo.LandAcquisitionStatisticsVo;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务结果序列化/反序列化工具类
 * 解决异步任务结果的类型转换问题
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
public class TaskResultSerializer {

    private static final String TYPE_KEY = "@type";
    private static final String DATA_KEY = "@data";
    
    /**
     * 序列化任务结果，保留类型信息
     */
    public static String serialize(Object result) {
        if (result == null) {
            return null;
        }
        
        try {
            Map<String, Object> wrapper = new HashMap<>();
            wrapper.put(TYPE_KEY, result.getClass().getName());
            wrapper.put(DATA_KEY, result);
            
            return JSON.toJSONString(wrapper);
        } catch (Exception e) {
            log.error("序列化任务结果失败", e);
            // 降级处理：使用简单JSON序列化
            return JSON.toJSONString(result);
        }
    }
    
    /**
     * 反序列化任务结果，恢复原始类型
     */
    public static Object deserialize(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            
            // 检查是否包含类型信息
            if (jsonObject.containsKey(TYPE_KEY) && jsonObject.containsKey(DATA_KEY)) {
                String typeName = jsonObject.getString(TYPE_KEY);
                Object data = jsonObject.get(DATA_KEY);
                
                return deserializeByType(typeName, data);
            } else {
                // 没有类型信息，尝试智能推断
                return smartDeserialize(jsonObject);
            }
        } catch (Exception e) {
            log.warn("反序列化任务结果失败，返回原始JSON字符串", e);
            return json;
        }
    }
    
    /**
     * 根据类型名称反序列化
     */
    private static Object deserializeByType(String typeName, Object data) {
        try {
            switch (typeName) {
                case "com.baomidou.mybatisplus.extension.plugins.pagination.Page":
                    return deserializePage(data);
                case "com.wflow.bean.vo.LandAcquisitionStatisticsVo":
                    return JSON.parseObject(JSON.toJSONString(data), LandAcquisitionStatisticsVo.class);
                default:
                    // 尝试通过反射加载类
                    Class<?> clazz = Class.forName(typeName);
                    return JSON.parseObject(JSON.toJSONString(data), clazz);
            }
        } catch (Exception e) {
            log.warn("根据类型反序列化失败，类型: {}", typeName, e);
            return data;
        }
    }
    
    /**
     * 反序列化Page对象
     */
    @SuppressWarnings("unchecked")
    private static Page<LandAcquisitionStatisticsVo> deserializePage(Object data) {
        try {
            JSONObject pageJson = (JSONObject) data;
            
            Page<LandAcquisitionStatisticsVo> page = new Page<>();
            
            // 设置分页信息
            if (pageJson.containsKey("current")) {
                page.setCurrent(pageJson.getLongValue("current"));
            }
            if (pageJson.containsKey("size")) {
                page.setSize(pageJson.getLongValue("size"));
            }
            if (pageJson.containsKey("total")) {
                page.setTotal(pageJson.getLongValue("total"));
            }
            if (pageJson.containsKey("pages")) {
                page.setPages(pageJson.getLongValue("pages"));
            }
            
            // 反序列化记录列表
            if (pageJson.containsKey("records")) {
                List<LandAcquisitionStatisticsVo> records = JSON.parseArray(
                        JSON.toJSONString(pageJson.get("records")), 
                        LandAcquisitionStatisticsVo.class);
                page.setRecords(records);
            }
            
            return page;
        } catch (Exception e) {
            log.error("反序列化Page对象失败", e);
            throw new RuntimeException("反序列化Page对象失败", e);
        }
    }
    
    /**
     * 智能反序列化：根据JSON结构推断类型
     */
    private static Object smartDeserialize(JSONObject jsonObject) {
        try {
            // 检查是否是Page对象
            if (jsonObject.containsKey("records") && jsonObject.containsKey("total") 
                && jsonObject.containsKey("current") && jsonObject.containsKey("size")) {
                return deserializePage(jsonObject);
            }
            
            // 检查是否是LandAcquisitionStatisticsVo
            if (jsonObject.containsKey("xzqCode") && jsonObject.containsKey("xzqName")) {
                return JSON.parseObject(JSON.toJSONString(jsonObject), LandAcquisitionStatisticsVo.class);
            }
            
            // 默认返回JSONObject
            return jsonObject;
        } catch (Exception e) {
            log.warn("智能反序列化失败", e);
            return jsonObject;
        }
    }
    
    /**
     * 专门用于征地统计结果的反序列化
     */
    public static Page<LandAcquisitionStatisticsVo> deserializeLandStatisticsResult(String json) {
        Object result = deserialize(json);
        
        if (result instanceof Page) {
            try {
                @SuppressWarnings("unchecked")
                Page<LandAcquisitionStatisticsVo> page = (Page<LandAcquisitionStatisticsVo>) result;
                return page;
            } catch (ClassCastException e) {
                log.warn("Page类型转换失败，尝试重新解析", e);
                // 如果类型转换失败，尝试重新解析
                return deserializePage(result);
            }
        } else if (result instanceof JSONObject) {
            return deserializePage(result);
        } else {
            log.warn("无法识别的征地统计结果类型: {}", result.getClass().getName());
            return new Page<>();
        }
    }
    
    /**
     * 检查对象是否为征地统计结果类型
     */
    public static boolean isLandStatisticsResult(Object obj) {
        if (obj == null) {
            return false;
        }
        
        if (obj instanceof Page) {
            Page<?> page = (Page<?>) obj;
            if (!page.getRecords().isEmpty()) {
                return page.getRecords().get(0) instanceof LandAcquisitionStatisticsVo;
            }
        }
        
        return false;
    }
    
    /**
     * 安全的类型转换方法
     */
    @SuppressWarnings("unchecked")
    public static <T> T safeCast(Object obj, Class<T> targetClass) {
        if (obj == null) {
            return null;
        }
        
        if (targetClass.isInstance(obj)) {
            return (T) obj;
        }
        
        // 尝试JSON转换
        try {
            String json = JSON.toJSONString(obj);
            return JSON.parseObject(json, targetClass);
        } catch (Exception e) {
            log.warn("类型转换失败，从 {} 到 {}", obj.getClass().getName(), targetClass.getName(), e);
            return null;
        }
    }
}
