package com.wflow.service;

import com.wflow.bean.entity.WflowNodeOperate;
    
/**
 *@Author：qinyi
 *@Date：2024-12-19  10:48
*/
public interface WflowNodeOperateService{
    Object createNodeOperate(WflowNodeOperate wflowNodeOperate);

    Object editNodeOperate(WflowNodeOperate wflowNodeOperate);

    Object delNodeOperate(String ids);

    Object innerList(String nodeId, String operateName, String operateSign);

    Object saveToOutList(String nodeOperateIds);

    Object getNodeOperateList(String nodeId,String operateName,String operateSign);

    Object delOutNodeOperate(String ids);

    Object copyFrom(String nodeId, String fromNodeId,Long modelHistorysId);

    Object relationOperaToNode(String nodeId, String operateIds);

    Object delNodeOperate2(String nodeId, String operateIds);
}
