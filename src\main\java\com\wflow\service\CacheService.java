package com.wflow.service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务接口
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
public interface CacheService {

    /**
     * 设置缓存
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void set(String key, Object value, long timeout, TimeUnit unit);

    /**
     * 获取缓存
     * @param key 缓存键
     * @param clazz 值类型
     * @return 缓存值
     */
    <T> T get(String key, Class<T> clazz);

    /**
     * 获取列表缓存
     * @param key 缓存键
     * @param clazz 列表元素类型
     * @return 缓存列表
     */
    <T> List<T> getList(String key, Class<T> clazz);

    /**
     * 删除缓存
     * @param key 缓存键
     */
    void delete(String key);

    /**
     * 检查缓存是否存在
     * @param key 缓存键
     * @return 是否存在
     */
    boolean exists(String key);

    /**
     * 设置缓存过期时间
     * @param key 缓存键
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void expire(String key, long timeout, TimeUnit unit);

    /**
     * 根据模式删除缓存
     * @param pattern 缓存键模式
     */
    void deleteByPattern(String pattern);
}
