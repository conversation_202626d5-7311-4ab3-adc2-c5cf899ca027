package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024-12-19  10:48
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "环节操作表")
public class WflowNodeOperate {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 操作名称
    */
    @Schema(description ="操作名称")
    private String operateName;

    /**
    * 显示名称
    */
    @Schema(description ="显示名称")
    private String showName;

    /**
    * 排序号
    */
    @Schema(description ="排序号")
    private Integer sortNo;

    /**
    * 图片路径
    */
    @Schema(description ="图片路径")
    private String imgurl;

    /**
    * 图标
    */
    @Schema(description ="图标")
    private String icon;

    /**
    * 操作标识
    */
    @Schema(description ="操作标识")
    private String operateSign;

    /**
    * 脚本名称
    */
    @Schema(description ="脚本名称")
    private String scriptName;

    /**
    * 扩展属性
    */
    @Schema(description ="扩展属性")
    private String extendedAttr;

    /**
    * 节点id
    */
    @Schema(description ="节点id")
    private String nodeId;

    /**
    * 节点名称
    */
    @Schema(description ="节点名称")
    private String nodeName;

    /**
    * 流程定义的 ID
    */
    @Schema(description ="流程定义的 ID")
    private String processDefId;

    /**
    * 父表主键id
    */
    @Schema(description ="父表主键id")
    private Long modelHistorysId;

    /**
    * 创建时间
    */
    @Schema(description ="创建时间")
    private LocalDateTime created;

    /**
     * 最终保存
     */
    @Schema(description ="最终保存")
    private Boolean isFinalSave;

    /**
     * step_operate_sign
     * 环节操作标识
     */
    @Schema(description ="环节操作标识")
    private String stepOperateSign;

    /**
     *显示条件
     * show_condition
     */
    @Schema(description ="显示条件")
    private String showCondition;

}