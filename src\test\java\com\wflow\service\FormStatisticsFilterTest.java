package com.wflow.service;

import com.wflow.service.impl.FormStatisticsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.*;

/**
 * 征地统计服务过滤功能测试
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@SpringBootTest
public class FormStatisticsFilterTest {

    @Autowired
    private FormStatisticsService formStatisticsService;

    @Test
    public void testCalculateFieldSumWithFilters() {
        log.info("=== 测试字段求和过滤功能 ===");

        // 测试不同的过滤条件
        testNoFilters();
        testCustomFieldFilters();
        testLegacyFilters();
        testMixedFilters();
    }

    @Test
    public void testCalculateCurrentRegionSummary() {
        log.info("=== 测试当前行政区汇总统计过滤功能 ===");

        // 测试calculateCurrentRegionSummary方法的filters参数
        testCurrentRegionSummaryWithFilters();
        testCurrentRegionSummaryWithCustomFields();
    }

    private void testCurrentRegionSummaryWithFilters() {
        log.info("--- 测试当前行政区汇总统计（传统参数） ---");

        Map<String, Object> filters = new HashMap<>();
        filters.put("isCompleted", true);
        filters.put("landStatusFieldId", "field_123");

        log.info("传统参数过滤条件: {}", filters);

        // 注意：这里无法直接测试private方法，但可以通过公共方法间接测试
        // 实际测试中需要通过征地统计查询接口来验证
    }

    private void testCurrentRegionSummaryWithCustomFields() {
        log.info("--- 测试当前行政区汇总统计（自定义字段） ---");

        Map<String, Object> filters = new HashMap<>();
        filters.put("field2976601246206", "想出去玩");
        filters.put("field6138601358008", "办公使用");

        log.info("自定义字段过滤条件: {}", filters);

        // 注意：这里无法直接测试private方法，但可以通过公共方法间接测试
        // 实际测试中需要通过征地统计查询接口来验证
    }

    private void testNoFilters() {
        log.info("--- 测试无过滤条件 ---");

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null, null);

            log.info("无过滤条件求和结果: {}", sum);

        } catch (Exception e) {
            log.error("无过滤条件测试失败", e);
        }
    }

    private void testCustomFieldFilters() {
        log.info("--- 测试自定义字段过滤 ---");

        Map<String, Object> filters = new HashMap<>();
        filters.put("field2976601246206", "想出去玩");
        filters.put("field6138601358008", "办公使用");

        log.info("自定义字段过滤条件: {}", filters);

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null, filters);

            log.info("自定义字段过滤求和结果: {}", sum);

        } catch (Exception e) {
            log.error("自定义字段过滤测试失败", e);
        }
    }

    private void testLegacyFilters() {
        log.info("--- 测试传统参数过滤 ---");

        Map<String, Object> filters = new HashMap<>();
        filters.put("isCompleted", true);
        filters.put("landStatusFieldId", "field_123");

        log.info("传统参数过滤条件: {}", filters);

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null, filters);

            log.info("传统参数过滤求和结果: {}", sum);

        } catch (Exception e) {
            log.error("传统参数过滤测试失败", e);
        }
    }

    private void testMixedFilters() {
        log.info("--- 测试混合过滤条件 ---");

        Map<String, Object> filters = new HashMap<>();
        filters.put("isCompleted", true);
        filters.put("landStatusFieldId", "field_123");
        filters.put("field2976601246206", "想出去玩");
        filters.put("field6138601358008", "办公使用");

        log.info("混合过滤条件: {}", filters);

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null, filters);

            log.info("混合过滤求和结果: {}", sum);

        } catch (Exception e) {
            log.error("混合过滤测试失败", e);
        }
    }

    @Test
    public void testFilterLogic() {
        log.info("=== 测试过滤逻辑 ===");

        // 测试过滤条件的AND逻辑
        testAndLogic();
        testEmptyResults();
        testPerformance();
    }

    private void testAndLogic() {
        log.info("--- 测试AND逻辑 ---");

        // 模拟多个过滤条件，验证AND逻辑
        Map<String, Object> filters = new HashMap<>();
        filters.put("field1", "value1");
        filters.put("field2", "value2");
        filters.put("field3", "value3");

        log.info("多条件过滤（AND逻辑）: {}", filters);

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null, filters);

            log.info("AND逻辑过滤结果: {}", sum);

        } catch (Exception e) {
            log.error("AND逻辑测试失败", e);
        }
    }

    private void testEmptyResults() {
        log.info("--- 测试空结果处理 ---");

        // 使用不存在的字段值，应该返回空结果
        Map<String, Object> filters = new HashMap<>();
        filters.put("nonexistent_field", "nonexistent_value");

        log.info("不存在的过滤条件: {}", filters);

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null, filters);

            log.info("空结果过滤: {}", sum);

            if (BigDecimal.ZERO.equals(sum)) {
                log.info("空结果处理正确");
            } else {
                log.warn("空结果处理可能有问题");
            }

        } catch (Exception e) {
            log.error("空结果测试失败", e);
        }
    }

    private void testPerformance() {
        log.info("--- 测试性能 ---");

        Map<String, Object> filters = new HashMap<>();
        filters.put("field2976601246206", "想出去玩");

        int testCount = 10;
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < testCount; i++) {
            try {
                formStatisticsService.calculateFieldSumWithOrgFilter(
                        "test_form", "test_field", true, null, filters);
            } catch (Exception e) {
                log.debug("性能测试第{}次调用失败", i + 1, e);
            }
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / testCount;

        log.info("性能测试结果:");
        log.info("  测试次数: {}", testCount);
        log.info("  总耗时: {}ms", totalTime);
        log.info("  平均耗时: {:.2f}ms", avgTime);
    }

    @Test
    public void testBackwardCompatibility() {
        log.info("=== 测试向后兼容性 ===");

        // 测试原有的调用方式
        testOriginalMethod();
        testParameterConversion();
    }

    private void testOriginalMethod() {
        log.info("--- 测试原有方法调用 ---");

        try {
            // 调用不带filters参数的方法
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null);

            log.info("原有方法调用结果: {}", sum);

        } catch (Exception e) {
            log.error("原有方法调用测试失败", e);
        }
    }

    private void testParameterConversion() {
        log.info("--- 测试参数转换 ---");

        // 模拟从旧参数转换为新的filters格式
        Boolean oldIsCompleted = true;
        String oldLandStatusFieldId = "field_123";

        Map<String, Object> newFilters = new HashMap<>();
        if (oldIsCompleted != null) {
            newFilters.put("isCompleted", oldIsCompleted);
        }
        if (oldLandStatusFieldId != null) {
            newFilters.put("landStatusFieldId", oldLandStatusFieldId);
        }

        log.info("参数转换结果: {} -> {}",
                "isCompleted=" + oldIsCompleted + ", landStatusFieldId=" + oldLandStatusFieldId,
                newFilters);

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null, newFilters);

            log.info("参数转换调用结果: {}", sum);

        } catch (Exception e) {
            log.error("参数转换测试失败", e);
        }
    }

    @Test
    public void testErrorHandling() {
        log.info("=== 测试错误处理 ===");

        // 测试各种异常情况
        testNullParameters();
        testInvalidFilters();
        testDatabaseErrors();
    }

    private void testNullParameters() {
        log.info("--- 测试null参数 ---");

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    null, null, true, null, null);

            log.info("null参数测试结果: {}", sum);

        } catch (Exception e) {
            log.info("null参数正确抛出异常: {}", e.getMessage());
        }
    }

    private void testInvalidFilters() {
        log.info("--- 测试无效过滤条件 ---");

        Map<String, Object> invalidFilters = new HashMap<>();
        invalidFilters.put("", "空字段名");
        invalidFilters.put(null, "null字段名");
        invalidFilters.put("validField", null);

        log.info("无效过滤条件: {}", invalidFilters);

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "test_form", "test_field", true, null, invalidFilters);

            log.info("无效过滤条件测试结果: {}", sum);

        } catch (Exception e) {
            log.error("无效过滤条件测试失败", e);
        }
    }

    private void testDatabaseErrors() {
        log.info("--- 测试数据库错误处理 ---");

        // 使用不存在的表单ID和字段ID
        Map<String, Object> filters = new HashMap<>();
        filters.put("field2976601246206", "想出去玩");

        try {
            BigDecimal sum = formStatisticsService.calculateFieldSumWithOrgFilter(
                    "nonexistent_form", "nonexistent_field", true, null, filters);

            log.info("数据库错误处理测试结果: {}", sum);

        } catch (Exception e) {
            log.info("数据库错误正确处理: {}", e.getMessage());
        }
    }
}
