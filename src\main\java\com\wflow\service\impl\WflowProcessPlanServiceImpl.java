package com.wflow.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wflow.bean.entity.WflowModelGroups;
import com.wflow.bean.vo.WflowProcessPlanVo;
import com.wflow.mapper.WflowModelGroupsMapper;
import com.wflow.utils.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wflow.mapper.WflowProcessPlanMapper;
import com.wflow.bean.entity.WflowProcessPlan;
import com.wflow.service.WflowProcessPlanService;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 *@Author：qinyi
 *@Date：2025-1-8  09:46
 */
@Slf4j
@Service
@Transactional
public class WflowProcessPlanServiceImpl implements WflowProcessPlanService{

    @Resource
    private WflowProcessPlanMapper wflowProcessPlanMapper;

    @Resource
    private WflowModelGroupsMapper wflowModelGroupsMapper;

    @Override
    public Object relationProcessGroup(WflowProcessPlanVo wflowProcessPlanVo) {

        //采用先删除后更新的方式
        this.wflowProcessPlanMapper.delete(
                new LambdaQueryWrapper<WflowProcessPlan>()
                        .eq(WflowProcessPlan::getPlatformAppConfId, wflowProcessPlanVo.getPlatformAppConfId()));
        wflowProcessPlanVo.getGroupIdList().forEach(groupId -> {
            WflowModelGroups wflowModelGroups = this.wflowModelGroupsMapper.selectOne(new LambdaQueryWrapper<WflowModelGroups>().eq(WflowModelGroups::getGroupId, groupId));
            WflowProcessPlan wflowProcessPlan = WflowProcessPlan.builder()
                    .groupId(groupId)
                    .platformAppConfId(wflowProcessPlanVo.getPlatformAppConfId())
                    .groupName(wflowModelGroups.getGroupName())
                    .operater(StpUtil.getLoginIdAsString())
                    .createTime(LocalDateTime.now())
                    .build();
            this.wflowProcessPlanMapper.insert(wflowProcessPlan);
        });
        return R.ok("专题关联分组成功");

    }

    @Override
    public Object getModelGroupList() {
        List<WflowModelGroups> wflowModelGroups = this.wflowModelGroupsMapper.selectList(new LambdaQueryWrapper<WflowModelGroups>());
        return wflowModelGroups;
    }

    @Override
    public Object getProcessPlanGroupList(Long platformAppConfId) {
        List<WflowProcessPlan> wflowProcessPlanList = this.wflowProcessPlanMapper.selectList(new LambdaQueryWrapper<WflowProcessPlan>().eq(WflowProcessPlan::getPlatformAppConfId, platformAppConfId));
        return wflowProcessPlanList;
    }

    @Override
    public Object delProcessPlanGroup(Long id) {
        int i = this.wflowProcessPlanMapper.deleteById(id);
        return i > 0 ? R.ok("删除成功") : R.serverError("删除失败");
    }
}
