package com.wflow.controller;

import com.wflow.bean.vo.WflowProcessPlanVo;
import com.wflow.service.ModelGroupService;
import com.wflow.service.WflowProcessPlanService;
import com.wflow.utils.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> qinyi
 * @date : 2024/12/24
 */
@RestController
@RequestMapping("wflow/processPlan")
@Tag(name = "流程方案管理")
public class ProcessPlanController {

    @Autowired
    private WflowProcessPlanService workflowProcessPlanService;

    @Autowired
    private ModelGroupService modelGroupService;

    @PostMapping("/relationProcessGroup")
    @Operation(summary = "专题关联流程分组")
    public Object relationProcessGroup(@RequestBody WflowProcessPlanVo wflowProcessPlanVo) {
        return workflowProcessPlanService.relationProcessGroup(wflowProcessPlanVo);
    }

    @GetMapping("/modelGroupList")
    @Operation(summary = "流程分组列表")
    public Object getModelGroupList() {
        return workflowProcessPlanService.getModelGroupList();
    }

    @GetMapping("/getProcessPlanGroupList")
    @Operation(summary = "流程方案分组列表")
    public Object getProcessPlanGroupList(@RequestParam Long platformAppConfId) {
        return workflowProcessPlanService.getProcessPlanGroupList(platformAppConfId);
    }

    @DeleteMapping("/del")
    @Operation(summary = "删除关联的流程方案分组")
    public Object delProcessPlanGroup(@RequestParam Long id) {
        return workflowProcessPlanService.delProcessPlanGroup(id);
    }

    /**
     * 获取用户可见的流程列表
     * @param modelName 流程模型名筛选
     * @return 列表数据
     */
    @GetMapping("list/byPlatform")
    @Operation(summary = "获取子系统可见的流程列表")
    public Object getUserModels(@RequestParam(required = false) String modelName,
                                @RequestParam Long platformAppConfId) {
        return R.ok(modelGroupService.getGroupModels2(platformAppConfId, modelName));
    }

}
