package com.wflow.controller;

import com.wflow.utils.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.Data;
import org.apache.commons.io.IOUtils;
import org.flowable.bpmn.BpmnAutoLayout;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.*;
import org.flowable.bpmn.model.Process;
import org.flowable.engine.*;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.image.impl.DefaultProcessDiagramGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bpmn")
@Tag(name = "获取流程图")
public class FormCheckController {
    @Autowired
    private RuntimeService runtimeService;
    @Resource
    private HistoryService historyService;
    @Resource
    private RepositoryService repositoryService;

    @Resource
    private ProcessEngine processEngine;

    public Object check(){
        return "check";
    }
    @RequestMapping("/delete")
    @Operation(summary ="删除任务")
    public Object check2(String instanId){
        runtimeService.deleteProcessInstance(
                instanId,
                "MANUALLY_TERMINATED"
        );
        historyService.deleteHistoricProcessInstance(instanId);

        return R.ok(instanId);
    }
    @GetMapping("/img")
    @Operation(summary ="获取流程图以base64格式返回给前端")
    public Object img(String instanId){
        // 1. 获取流程引擎
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        RepositoryService repositoryService = processEngine.getRepositoryService();
        RuntimeService runtimeService = processEngine.getRuntimeService();

        // 2. 获取 diagramGenerator
        ProcessEngineConfiguration config = processEngine.getProcessEngineConfiguration();
        ProcessDiagramGenerator diagramGenerator = config.getProcessDiagramGenerator();

        // 3. 获取流程实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(instanId)
                .singleResult();

        // 4. 获取活动节点
        List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstance.getId());

        // 5. 获取BPMN模型
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(processInstance.getProcessDefinitionId())
                .singleResult();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());

        // 6. 生成带高亮的流程图 - 使用 DefaultProcessDiagramGenerator
        try (InputStream imageStream = diagramGenerator.generateDiagram(
                bpmnModel,
                "png",
                activeActivityIds,
                Collections.emptyList(),
                "宋体",
                "宋体",
                "宋体",
               config.getClassLoader(),
                1.0,
                true)) {

            // 保存到文件
            byte[] imageBytes = IOUtils.toByteArray(imageStream);
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            // 构建前端可直接使用的Data URL

            // 返回Base64数据（根据前端需求调整返回结构）
            return "data:image/png;base64," + base64Image;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    @GetMapping("/export-bpmn")
    @Operation(summary ="获取bpmn以xml格式返回给前端")
    public String exportBpmn(@RequestParam String processDefId) {
        RepositoryService repositoryService = processEngine.getRepositoryService();

        // 查询流程定义
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(processDefId)
                .singleResult();

        if (processDefinition == null) {
            throw new RuntimeException("流程定义不存在: " + processDefId);
        }

        // 获取 BPMN XML 资源流
        try (InputStream bpmnStream = repositoryService.getResourceAsStream(
                processDefinition.getDeploymentId(),
                processDefinition.getResourceName()
        )) {
            if (bpmnStream == null) {
                throw new RuntimeException("BPMN 资源未找到");
            }

            // 直接读取流内容并返回
            return IOUtils.toString(bpmnStream, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("获取 BPMN 内容失败", e);
        }
    }
    // 根据流程实例ID获取流程定义BPMN XML
    @GetMapping("/bpmnXml/{processInstanceId}")
    @Operation(summary ="bpmn.xml获取")
    public ResponseEntity<String> getBpmnXml(@PathVariable String processInstanceId) throws IOException {
        // 获取流程实例
        ProcessInstance instance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult();

        if (instance == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Process instance not found");
        }

        String processDefinitionId = instance.getProcessDefinitionId();

        // 通过流程定义ID获取BPMN XML
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(processDefinitionId).singleResult();

        if (processDefinition == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Process definition not found");
        }

        // 获取BPMN XML资源流
        InputStream bpmnStream = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(),
                processDefinition.getResourceName());
        BpmnModel model = repositoryService.getBpmnModel(processDefinitionId);
        String bpmnXml = new String(bpmnStream.readAllBytes(), StandardCharsets.UTF_8);
        String grop = getGraphMl(model);
        return ResponseEntity.ok(grop);
    }

    // 保存更新后的BPMN XML（示例，复杂点，流程定义需重新部署）
    @PostMapping("/bpmnXml/update")
    public ResponseEntity<String> updateBpmnXml(@RequestParam String deploymentId,
                                                @RequestBody String newBpmnXml) {
        // Flowable不支持直接更新已部署流程，需要重新部署
        // 这里示例直接用新XML重新部署，生产中需加版本管理

        repositoryService.deleteDeployment(deploymentId, true);

        repositoryService.createDeployment()
                .addString("updatedProcess.bpmn20.xml", newBpmnXml)
                .deploy();

        return ResponseEntity.ok("Process updated and redeployed");
    }
    private String getGraphMl(BpmnModel model){
        Collection<FlowElement> flowElements = model.getMainProcess().getFlowElements();

        List<String> nodeIds = new ArrayList<>();
        List<String> graphmlNodes = new ArrayList<>();
        List<String> graphmlEdges = new ArrayList<>();

        for (FlowElement element : flowElements) {
            if (!(element instanceof SequenceFlow)) {
                String id = element.getId();
                nodeIds.add(id);
                graphmlNodes.add("<node id=\"" + id + "\"/>");
            }
        }

// 添加边
        for (FlowElement element : flowElements) {
            if (element instanceof SequenceFlow flow) {
                String edge = String.format("<edge source=\"%s\" target=\"%s\"/>", flow.getSourceRef(), flow.getTargetRef());
                graphmlEdges.add(edge);
            }
        }

        return "<graphml xmlns=\"http://graphml.graphdrawing.org/xmlns\">\n" +
                "  <graph edgedefault=\"directed\">\n" +
                String.join("\n", graphmlNodes) + "\n" +
                String.join("\n", graphmlEdges) + "\n" +
                "  </graph>\n" +
                "</graphml>";
    }
}
