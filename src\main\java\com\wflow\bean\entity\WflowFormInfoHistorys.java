package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
    * 表单(材料)表
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "表单(材料)历史信息表")
public class WflowFormInfoHistorys implements Serializable {
    private static final long serialVersionUID = 478762346225445872L;

    @TableId(type = IdType.ASSIGN_ID) // 手动赋值主键
    private String hId;

    /**
    * 表单设置内容 JSON
    */
    private String formItems;

    /**
    * 材料名称
    */
    private String materialName;

    /**
    * 表单全局设置
    */
    private String formConfig;

    /**
    * 创建时间
    */
    private Date created;

    /**
    * 材料显示名称
    */
    private String materialShowName;

    /**
    * 所属子系统(分组)id
    */
    private String belongGroupId;

    /**
    * 材料标识
    */
    private String materialSign;

    /**
    * 材料分类
    */
    private String materialClassify;

    /**
    * 排序
    */
    private Integer sort;

    /**
    * 产生
    */
    private String generate;

    /**
    * 材料类型
    */
    private String materialType;

    /**
    * 显示位置
    */
    private String showPosition;

    /**
    * 打开方式
    */
    private String openWith;

    /**
    * 约束
    */
    private String formConstraint;

    /**
    * 类型
    */
    private String typesof;

    /**
    * 所属目录id
    */
    private String formDirId;

    /**
    * 当前版本
    */
    private Integer version;

    private String id;

}