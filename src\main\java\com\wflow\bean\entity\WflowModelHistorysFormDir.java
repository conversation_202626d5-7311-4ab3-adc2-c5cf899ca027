package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author：qinyi
 * @Date：2024-11-20 16:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "流程与材料目录关联表")
public class WflowModelHistorysFormDir implements Serializable {

    private static final long serialVersionUID = 478762346225443838L;


    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    //form_items
    @Schema(description ="流程表id")
    private String modelHistorysId;

    //material_name
    @Schema(description ="材料目录id")
    private String formDirId;
}
