package com.wflow.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.GeometryFactory;
import com.vividsolutions.jts.geom.Point;
import com.wflow.bean.dto.CoordinateListVo;
import lombok.extern.slf4j.Slf4j;
import org.geotools.geometry.jts.JTS;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.geotools.referencing.CRS;
import org.geotools.referencing.GeodeticCalculator;
import org.opengis.referencing.crs.CoordinateReferenceSystem;
import org.opengis.referencing.operation.MathTransform;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 */
@Slf4j
public class Txt2GeoJsonUtil {


    /**
     * <h2>txt2GeoJson</h2>
     * @param txtFilePath: 文本路径
     * @param zone: 度带
     */
    public static Object txt2GeoJson(String txtFilePath, String zone) throws IOException {

        // 定义输入的国土报备坐标 TXT 文件路径
//         txtFilePath = "D:\\20236131730981.txt";
        JSONObject geoJsonObjectVo = new JSONObject();
        JSONObject geoJsonObject = new JSONObject();
        geoJsonObject.put("type", "FeatureCollection");
        List<CoordinateListVo> coordinateListVos = new ArrayList<>(List.of());

        // feature 集合
        List<JSONObject> features = new ArrayList<JSONObject>();

        try (BufferedReader reader = new BufferedReader(new FileReader(txtFilePath))) {
            String line, dh = "J0", dkqh = "1";
            // feature开始构建的标志
            boolean begin = false;
            JSONObject feature = null, geometry = null;

            // 放坐标点集合
            List<List<Double>> coordes = new ArrayList<>();
            // 放圈号集合
            List<List<List<Double>>> coordesFather = new ArrayList<>();
            // 放地块集合
            List<List<List<List<Double>>>> coordesSuper = new ArrayList<>();
            HashMap<String,Coordinate> coordinateHashMap = new HashMap<>();
            while ((line = reader.readLine()) != null) {
                if(line.contains("带号")){

                }
                // 第一个feature开始,构建属性
                if (line.endsWith("@")) {

                    // 读到新的一个feature，把上一个feature存起来
                    if (line.endsWith("@") && begin) {
                        coordesSuper.add(new ArrayList<>(coordesFather));
                        coordesFather.clear();
                        geometry.put("coordinates", new ArrayList<>(coordesSuper));
                        coordesSuper.clear();
                        feature.put("geometry", geometry);
                        features.add(feature);
                        // begin = false;
                    }
                    begin = true;

                    // 创建实例
                    feature = new JSONObject();
                    feature.put("type", "Feature");
                    geometry = new JSONObject();
                    geometry.put("type", "MultiPolygon");

                    String[] params = line.split(",");
                    // 构建属性信息
                    Map<String, Object> properties = new HashMap<>();
                    properties.put("界址点数", params[0]);
                    properties.put("地块面积", params[1]);
                    properties.put("地块编号", params[2]);
                    properties.put("地块名称", params[3]);
                    properties.put("图形属性", params[4]);
                    properties.put("图幅号", params[5]);
                    properties.put("地块用途", params[6]);
                    properties.put("地类编码", params[7]);
                    feature.put("properties", properties);
                    feature.put("id", SnowflakeUtil.snowflakeId());
                    geoJsonObjectVo.put("landNumber", params[2]);
                    geoJsonObjectVo.put("landName", params[3]);
                    geoJsonObjectVo.put("landArea", params[1]);
                    geoJsonObjectVo.put("landPointNumber", params[0]);
                    geoJsonObjectVo.put("landType", params[4]);
                }

                // 构建第一个feature的坐标点
                if (!line.endsWith("@") && begin) {
                    // 解析每一行坐标数据 J1,1,4239948.191,37562423.81
                    String[] parts = line.split(",");

                    double longitude = Double.parseDouble(parts[2]);
                    double latitude = Double.parseDouble(parts[3]);
                    //判断y是否是37或者38开头
                    BigDecimal bigDecimal = new BigDecimal(Double.toString(latitude));
                    String plainString = bigDecimal.toPlainString();
                    if (plainString.length()>=2 && StrUtil.isEmpty(zone)){
                        String substring = plainString.substring(0, 2);
                        if ("37".equals(substring)){
                            zone = "4525";
                        }else if ("38".equals(substring)){
                            zone = "4526";
                        }else {
                            zone = "4525";
                        }
                    }
                    // 创建投影坐标点
                    Coordinate projectionCoordinate = new Coordinate(longitude, latitude);
                    GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();
                    Point geometryFactoryPoint = geometryFactory.createPoint(projectionCoordinate);

                    // 定义投影坐标系和地理坐标系
                    CoordinateReferenceSystem sourceCRSObject = CRS.decode("EPSG:" + zone);
                    CoordinateReferenceSystem targetCRSObject = CRS.decode("EPSG:4490");

                    // 创建坐标变换对象
                    MathTransform transform = CRS.findMathTransform(sourceCRSObject, targetCRSObject, true);

                    // 进行坐标转换
                    Coordinate coordinate = JTS.transform(geometryFactoryPoint, transform).getCoordinate();

                    ArrayList<Double> coord = Lists.newArrayList(coordinate.y, coordinate.x);
                    String distance = "";
                    // 计算距离
                    if(!coordinateHashMap.isEmpty()){
                        GeodeticCalculator calculator = new GeodeticCalculator(targetCRSObject);
                        calculator.setStartingPosition(JTS.toDirectPosition(coordinateHashMap.get("1"),targetCRSObject));
                        calculator.setDestinationPosition(JTS.toDirectPosition(coordinate,targetCRSObject));
                        distance = String.valueOf(calculator.getOrthodromicDistance());
                    }

                    // 构建坐标集合
                    if (strCompareTo(dh, parts[0])) {
                        coordes.add(coord);
                    } else {
                        if (StrUtil.equals(dkqh, parts[1])) {
                            coordes.add(coord);
                            coordesFather.add(new ArrayList<>(coordes));
                            coordes.clear();
                        }
                    }

                    // 点号
                    dh = parts[0];
                    // 地块圈号
                    dkqh = parts[1];
                    coordinateListVos.add(CoordinateListVo.builder()
                            .name(dh)
                            .x(parts[2])
                            .y(parts[3])
                            .length(distance)
                            .number(dkqh)
                            .build());
                    coordinateHashMap.put("1",coordinate);
                }
            }

            // 读完最后一行不会进入循环，所以需要在这里处理
            if (begin) {
                coordesSuper.add(new ArrayList<>(coordesFather));
                coordesFather.clear();
                geometry.put("coordinates", new ArrayList<>(coordesSuper));
                coordesSuper.clear();
                feature.put("geometry", geometry);
                features.add(feature);
            }
            // 添加到geojsonObject
            geoJsonObject.put("features", features);
            geoJsonObjectVo.put("features",geoJsonObject);
            geoJsonObjectVo.put("coordinateList",coordinateListVos);
        } catch (Exception e) {
            log.error("解析文件异常", e);
        }

        return R.ok(geoJsonObjectVo);
    }
    /**
     * <h2>strCompareTo</h2>
     * @param str1:
     * @param str2:
     * @return str1 < str2 true
     *  str1 > str2 false
     * <AUTHOR>
     * @date 2024/1/21 16:53
     */
    public static boolean strCompareTo(String str1, String str2) {
        // 提取出连续的数字
        String num1 = str1.replaceAll("\\D+", "");
        String num2 = str2.replaceAll("\\D+", "");

        BigDecimal bg1 = new BigDecimal(num1);
        BigDecimal bg2 = new BigDecimal(num2);

        return bg1.compareTo(bg2) <= 0;
    }


}
