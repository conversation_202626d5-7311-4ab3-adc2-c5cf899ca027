<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wflow.mapper.WflowFormInfoHistorysMapper">
  <resultMap id="BaseResultMap" type="com.wflow.bean.entity.WflowFormInfoHistorys">
    <!--@mbg.generated-->
    <!--@Table wflow_form_info_historys-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_items" jdbcType="VARCHAR" property="formItems" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="form_config" jdbcType="VARCHAR" property="formConfig" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="material_show_name" jdbcType="VARCHAR" property="materialShowName" />
    <result column="belong_group_id" jdbcType="BIGINT" property="belongGroupId" />
    <result column="material_sign" jdbcType="VARCHAR" property="materialSign" />
    <result column="material_classify" jdbcType="VARCHAR" property="materialClassify" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="generate" jdbcType="VARCHAR" property="generate" />
    <result column="material_type" jdbcType="VARCHAR" property="materialType" />
    <result column="show_position" jdbcType="VARCHAR" property="showPosition" />
    <result column="open_with" jdbcType="VARCHAR" property="openWith" />
    <result column="form_constraint" jdbcType="VARCHAR" property="formConstraint" />
    <result column="typesof" jdbcType="VARCHAR" property="typesof" />
    <result column="form_dir_id" jdbcType="BIGINT" property="formDirId" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, form_items, material_name, form_config, created, material_show_name, belong_group_id, 
    material_sign, material_classify, sort, generate, material_type, show_position, open_with, 
    form_constraint, typesof, form_dir_id, version
  </sql>
</mapper>