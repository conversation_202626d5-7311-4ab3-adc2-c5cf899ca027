package com.wflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.*;
import com.wflow.bean.enums.MatchTypeEnum;
import com.wflow.bean.vo.FormMenuVo;
import com.wflow.bean.vo.FormStatisticsVo;
import com.wflow.bean.vo.LandAcquisitionStatisticsVo;
import com.wflow.mapper.*;
import com.wflow.service.CacheService;
import com.wflow.service.FormStatisticsService;
import com.wflow.service.OrgRepositoryService;
import com.wflow.utils.TaskResultSerializer;
import com.wflow.utils.UserUtil;
import com.wflow.workflow.bean.vo.FormInsideVo;
import com.wflow.workflow.bean.vo.LinkVo;
import com.wflow.workflow.bean.vo.StatisticsHomeVo;
import com.wflow.workflow.utils.FlowableUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 表单统计服务实现类
 *
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@Service
public class FormStatisticsServiceImpl implements FormStatisticsService {

    @Autowired
    private WflowFormStatisticsConfigMapper statisticsConfigMapper;

    @Autowired
    private WflowFormStatisticsResultMapper statisticsResultMapper;

    @Autowired
    private WflowFormDataMapper formDataMapper;

    @Autowired
    private WflowModelsMapper modelsMapper;

    @Resource
    private WflowModelHistorysMapper modelHistorysMapper;

    @Autowired
    private WflowProcessInstancesMapper processInstancesMapper;

    @Autowired
    private OrgRepositoryService orgRepositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Resource
    private WflowRolesMapper rolesMapper;
    @Resource
    private  WflowIInstitutionMapper institutionMapper;
    @Resource
    private WflowUsersMapper usersMapper;
    @Resource
    private SysXzq14Mapper sysXzq14Mapper;
    @Resource
    private CacheService cacheService;
    @Override
    @Transactional
    public String createStatisticsConfig(WflowFormStatisticsConfig config) {
        config.setId(IdUtil.objectId());
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        config.setCreateBy(UserUtil.getLoginUserId());
        config.setStatus(1);
        statisticsConfigMapper.insert(config);
        log.info("创建统计配置成功，配置ID: {}", config.getId());
        return config.getId();
    }

    @Override
    @Transactional
    public boolean updateStatisticsConfig(WflowFormStatisticsConfig config) {
        config.setUpdateTime(LocalDateTime.now());
        config.setUpdateBy(UserUtil.getLoginUserId());

        int result = statisticsConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新统计配置成功，配置ID: {}", config.getId());
            // 清除相关缓存
            clearStatisticsCache(config.getId());
        }
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteStatisticsConfig(String configId) {
        int result = statisticsConfigMapper.deleteById(configId);
        if (result > 0) {
            log.info("删除统计配置成功，配置ID: {}", configId);
            // 清除相关缓存
            clearStatisticsCache(configId);
        }
        return result > 0;
    }

    @Override
    @Transactional
    public boolean toggleStatisticsConfig(String configId, Integer status) {
        WflowFormStatisticsConfig config = new WflowFormStatisticsConfig();
        config.setId(configId);
        config.setStatus(status);
        config.setUpdateTime(LocalDateTime.now());
        config.setUpdateBy(UserUtil.getLoginUserId());

        int result = statisticsConfigMapper.updateById(config);
        if (result > 0) {
            log.info("切换统计配置状态成功，配置ID: {}, 状态: {}", configId, status);
            if (status == 0) {
                // 禁用时清除缓存
                clearStatisticsCache(configId);
            }
        }
        return result > 0;
    }

    @Override
    public Page<WflowFormStatisticsConfig> getStatisticsConfigs(Integer pageNo, Integer pageSize,
                                                                String formId, Integer status) {
        Page<WflowFormStatisticsConfig> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<WflowFormStatisticsConfig> wrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(formId)) {
            wrapper.eq(WflowFormStatisticsConfig::getFormId, formId);
        }
        if (status != null) {
            wrapper.eq(WflowFormStatisticsConfig::getStatus, status);
        }

        wrapper.orderByDesc(WflowFormStatisticsConfig::getCreateTime);
        return statisticsConfigMapper.selectPage(page, wrapper);
    }

    @Override
    public List<WflowFormStatisticsConfig> getStatisticsConfigsByFormId(String formId) {
        return statisticsConfigMapper.selectEnabledByFormId(formId);
    }

    @Override
    public FormStatisticsVo executeRealTimeStatistics(String configId, boolean includeFinished) {
        WflowFormStatisticsConfig config = statisticsConfigMapper.selectById(configId);
        if (config == null || config.getStatus() != 1) {
            throw new RuntimeException("统计配置不存在或已禁用");
        }

        return doStatistics(config, includeFinished, null);
    }

    @Override
    public List<FormStatisticsVo> executeBatchStatistics(List<String> configIds, boolean includeFinished) {
        List<FormStatisticsVo> results = new ArrayList<>();

        for (String configId : configIds) {
            try {
                FormStatisticsVo result = executeRealTimeStatistics(configId, includeFinished);
                results.add(result);
            } catch (Exception e) {
                log.error("执行统计失败，配置ID: {}", configId, e);
            }
        }

        return results;
    }

    @Override
    public List<FormStatisticsVo> executeStatisticsByFormId(String formId, boolean includeFinished) {
        List<WflowFormStatisticsConfig> configs = getStatisticsConfigsByFormId(formId);
        if (CollectionUtil.isEmpty(configs)) {
            return Collections.emptyList();
        }

        List<String> configIds = configs.stream()
                .map(WflowFormStatisticsConfig::getId)
                .collect(Collectors.toList());

        return executeBatchStatistics(configIds, includeFinished);
    }

    @Override
    public List<String> getFieldDistinctValues(String formId, String fieldId, Integer limit) {
        LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormData::getCode, formId)
                .eq(WflowFormData::getFieldId, fieldId)
                .isNotNull(WflowFormData::getFieldValue)
                .ne(WflowFormData::getFieldValue, "")
                .groupBy(WflowFormData::getFieldValue)
                .orderByDesc(WflowFormData::getCreateTime);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        List<WflowFormData> dataList = formDataMapper.selectList(wrapper);
        return dataList.stream()
                .map(WflowFormData::getFieldValue)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean refreshStatisticsCache(String configId) {
        try {
            // 清除旧缓存
            clearStatisticsCache(configId);

            // 重新计算并缓存
            FormStatisticsVo result = executeRealTimeStatistics(configId, true);

            // 保存到缓存表
            saveStatisticsToCache(configId, result);

            log.info("刷新统计缓存成功，配置ID: {}", configId);
            return true;
        } catch (Exception e) {
            log.error("刷新统计缓存失败，配置ID: {}", configId, e);
            return false;
        }
    }
    /*
     * 查询菜单
     * */
    @Override
    public List<FormMenuVo> getMenu(String type) {
        return statisticsConfigMapper.selectList(
                        Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                                .eq(WflowFormStatisticsConfig::getConfigName, type))
                .stream()
                .map(config -> {
                            FormMenuVo vo = new FormMenuVo();
                            vo.setFieldId(config.getFieldId());
                            vo.setFieldName(config.getFieldName());
                            vo.setConfigName(config.getConfigName());
                            vo.setFormItem(JSONObject.parseObject(config.getFormItem()));
                            return vo;
                        }
                )
                .toList();
    }

    @Override
    public Object getstatistics(String type) {
        String userId = UserUtil.getLoginUserId();
        User user = usersMapper.selectById(Long.valueOf(userId));
        //查询用户角色
        List<WflowRoles> roles = rolesMapper.getRole(userId,"1729671487141314562");
        List<String> org = List.of();
        if(roles.isEmpty()){
            //查询机构和子机构
            org = orgRepositoryService.getRecursiveSubInstitution(String.valueOf(user.getInstitutionId()));
        }
        List<WflowFormStatisticsConfig> wflowFormStatisticsConfigs = statisticsConfigMapper.selectList(Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                .eq(WflowFormStatisticsConfig::getConfigName, "节点统计"));
//        List<String> defId = modelHistorysMapper.selectList(Wrappers.<WflowModelHistorys>lambdaQuery()
//                        .select(WflowModelHistorys::getProcessDefId)
//                        .eq(WflowModelHistorys::getBusinessType,type))
//                .stream()
//                .map(WflowModelHistorys::getProcessDefId)
//                .distinct()
//                .toList();
        List<String> defId = statisticsConfigMapper.selectList(Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                .eq(WflowFormStatisticsConfig::getConfigName, "报件统计"))
                .stream()
                .map(WflowFormStatisticsConfig::getFieldId)
                .distinct()
                .toList();
        //查询总流程数
        long totol=0L;
        if(!defId.isEmpty()){
            totol = FlowableUtils.getProcessCount(defId);   
        }
        // 查询处于指定节点的未结束活动实例数量
        List<LinkVo> linkVos = wflowFormStatisticsConfigs.stream().map(map->{
            long count = historyService.createHistoricActivityInstanceQuery()
                    .activityId(map.getFieldId())
                    .unfinished()
                    .count();
            return LinkVo.builder().total(count).id(map.getFieldId()).name(map.getFieldName()).build();

        }).toList();
        long mess = 0L;
        long link = 0L;
        //查询表单内节点统计数量
        if(!linkVos.isEmpty()){
            for (LinkVo linkVo:linkVos){
                String name = linkVo.getName();
                if(name.contains("公开")){
                    mess = linkVo.getTotal();
                }else if(name.contains("环节")){
                    link = linkVo.getTotal();
                }

            }
        }

       long finished = 0L;
       long unFinished = 0L;
        //查询表单内节点统计数量
        List<FormInsideVo> formInsideVos =  doHome(org).stream().map(map-> FormInsideVo.builder()
                .id(map.getFieldId())
                .name(map.getFieldName())
                .total(String.valueOf(map.getTotalMatchCount()))
                .build()).toList();
        if(!formInsideVos.isEmpty()){
            for (FormInsideVo formInsideVo:formInsideVos){
                String name = formInsideVo.getName();
                if(name.contains("已完成")){
                    finished = Long.parseLong(formInsideVo.getTotal());
                }else if(name.contains("未完成")){
                    unFinished = Long.parseLong(formInsideVo.getTotal());
                }

            }
        }
        return StatisticsHomeVo.builder()
                .total(totol)
                .message(mess)
                .link(link)
                .finished(finished)
                .unfinished(unFinished)
                .build();
    }

    @Override
    public Object getXzpStatistics(String xzqCode,String fieldId,String formId) {
        List<String> org = institutionMapper.selectList(
                        Wrappers.<SafetyInstitution>lambdaQuery()
                                .eq(SafetyInstitution::getXzqCode, xzqCode)
                ).stream()
                .flatMap(safetyInstitution -> {
                    // 获取父机构ID
                    String parentId = String.valueOf(safetyInstitution.getId());

                    // 获取所有递归子机构
                    List<String> subInstitutions = orgRepositoryService.getRecursiveSubInstitution(parentId);

                    // 创建包含父机构+所有子机构的流
                    return Stream.concat(
                            Stream.of(parentId),          // 父机构ID作为第一个元素
                            subInstitutions.stream()      // 所有子机构流
                    );
                }).distinct()
                .toList();
        return statisticsConfigMapper.selectList(Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                        .eq(StringUtils.isNotEmpty(fieldId), WflowFormStatisticsConfig::getFieldId, fieldId)
                        .eq(StringUtils.isNotEmpty(formId), WflowFormStatisticsConfig::getFormId, formId)
                ).stream()
                .findFirst() // 只取第一个匹配的配置
                .map(config -> {
                    FormStatisticsVo formStatisticsVo = doStatistics(config, true, org);
                    return FormInsideVo.builder()
                            .id(formStatisticsVo.getFieldId())
                            .name(formStatisticsVo.getFieldName())
                            .total(String.valueOf(formStatisticsVo.getTotalMatchCount()))
                            .build();
                })
                .orElse(null);
    }

    /**
     * 清除统计缓存
     */
    private void clearStatisticsCache(String configId) {
        LambdaQueryWrapper<WflowFormStatisticsResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormStatisticsResult::getConfigId, configId);
        statisticsResultMapper.delete(wrapper);
    }

    /**
     * 保存统计结果到缓存
     */
    private void saveStatisticsToCache(String configId, FormStatisticsVo result) {
        for (FormStatisticsVo.StatisticsDetail detail : result.getDetails()) {
            WflowFormStatisticsResult cacheResult = WflowFormStatisticsResult.builder()
                    .id(IdUtil.objectId())
                    .configId(configId)
                    .formId(result.getFormId())
                    .fieldId(result.getFieldId())
                    .targetValue(detail.getTargetValue())
                    .matchCount(detail.getMatchCount())
                    .totalCount(result.getTotalInstanceCount())
                    .matchRate(detail.getMatchRate())
                    .lastUpdateTime(LocalDateTime.now())
                    .build();

            statisticsResultMapper.insert(cacheResult);
        }
    }
    private List<FormStatisticsVo> doHome(List<String> org){
        List<WflowFormStatisticsConfig> wflowFormStatisticsConfigs = statisticsConfigMapper.selectList(Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                .eq(WflowFormStatisticsConfig::getConfigName, "首页统计"));
        if(!wflowFormStatisticsConfigs.isEmpty()){
            return wflowFormStatisticsConfigs.stream().map(config -> doStatistics(config,true,org)).toList();
        }
        return Collections.emptyList();
    }

    /**
     * 执行统计逻辑
     */
    private FormStatisticsVo doStatistics(WflowFormStatisticsConfig config, boolean includeFinished,List<String> org) {
        // 解析目标值
        List<String> targetValues = parseTargetValues(config.getTargetValue());

        // 获取表单信息
        WflowModels model = modelsMapper.selectById(config.getFormId());
        String formName = model != null ? model.getFormName() : "未知表单";

        // 查询表单数据
        List<WflowFormData> formDataList = getFormDataByConfig(config, includeFinished, org);

        // 获取所有相关的流程实例
        Set<String> instanceIds = formDataList.stream()
                .map(WflowFormData::getInstanceId)
                .collect(Collectors.toSet());


        Map<String, HistoricProcessInstance> instanceMap = getProcessInstanceMap(instanceIds);
        Map<String, String> userNameMap = getUserNameMap(instanceMap.values());

        // 执行匹配统计
        List<FormStatisticsVo.StatisticsDetail> details = new ArrayList<>();
        BigDecimal totalMatchCount = BigDecimal.ZERO;
        if("SUM".equals(config.getMatchType())){
            totalMatchCount =  calculateFieldSum(config.getFormId(),config.getFieldId(),true);
        }else {
            for (String targetValue : targetValues) {
                List<WflowFormData> matchedData = filterMatchedData(formDataList, targetValue, config.getMatchType());
                List<FormStatisticsVo.MatchedInstance> matchedInstances = buildMatchedInstances(
                        matchedData, instanceMap, userNameMap);

                BigDecimal matchCount = BigDecimal.valueOf(matchedData.size());
                totalMatchCount = totalMatchCount.add(matchCount);

//                BigDecimal matchRate = calculateMatchRate(matchCount, formDataList.size());
//
//                FormStatisticsVo.StatisticsDetail detail = FormStatisticsVo.StatisticsDetail.builder()
//                        .targetValue(targetValue)
//                        .matchCount(matchCount)
//                        .matchRate(matchRate)
//                        .matchedInstances(matchedInstances)
//                        .build();
//
//                details.add(detail);
            }
        }

//        BigDecimal totalMatchRate = calculateMatchRate(totalMatchCount, formDataList.size());

        return FormStatisticsVo.builder()
                .configId(config.getId())
                .configName(config.getConfigName())
                .formId(config.getFormId())
                .formName(formName)
                .fieldId(config.getFieldId())
                .fieldName(config.getFieldName())
                .targetValues(targetValues)
                .matchType(config.getMatchType())
                .totalMatchCount(totalMatchCount)
                .totalInstanceCount(formDataList.size())
                .statisticsTime(LocalDateTime.now())
                .build();
    }

    /**
     * 解析目标值
     */
    private List<String> parseTargetValues(String targetValueJson) {
        try {
            if (targetValueJson.startsWith("[")) {
                // JSON数组格式
                JSONArray jsonArray = JSON.parseArray(targetValueJson);
                return jsonArray.toJavaList(String.class);
            } else {
                // 单个值
                return Collections.singletonList(targetValueJson);
            }
        } catch (Exception e) {
            log.warn("解析目标值失败，使用原始值: {}", targetValueJson, e);
            return Collections.singletonList(targetValueJson);
        }
    }

    /**
     * 根据配置查询表单数据（支持filters过滤）
     */
    private List<WflowFormData> getFormDataByConfig(WflowFormStatisticsConfig config, boolean includeFinished,
            List<String> org, Map<String, Object> filters) {

        // 1. 首先根据filters获取符合条件的实例ID集合
        Set<String> filteredInstanceIds = null;
        if (filters != null && !filters.isEmpty()) {
            // 提取自定义字段过滤条件（排除传统参数）
            Map<String, Object> customFilters = new HashMap<>(filters);
            customFilters.remove("isCompleted");
            customFilters.remove("landStatusFieldId");

            if (!customFilters.isEmpty()) {
                filteredInstanceIds = buildFormDataFilterConditions(customFilters);
                log.debug("根据自定义字段过滤得到实例ID数量: {}",
                        filteredInstanceIds != null ? filteredInstanceIds.size() : 0);
            }
        }

        // 2. 构建基础查询条件
        LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormData::getCode, config.getFormId())
                .eq(WflowFormData::getFieldId, config.getFieldId())
                .isNotNull(WflowFormData::getFieldValue)
                .ne(WflowFormData::getFieldValue, "");

        // 3. 应用实例ID过滤条件
        if (filteredInstanceIds != null && !filteredInstanceIds.isEmpty()) {
            wrapper.in(WflowFormData::getInstanceId, filteredInstanceIds);
        } else if (filteredInstanceIds != null && filteredInstanceIds.isEmpty()) {
            // 如果过滤条件存在但没有匹配的实例，直接返回空列表
            log.debug("过滤条件没有匹配到任何实例，返回空结果");
            return new ArrayList<>();
        }

        // 4. 执行查询
        List<WflowFormData> allData = formDataMapper.selectList(wrapper);

        // 5. 如果没有机构过滤条件，直接返回结果
        if (org == null || org.isEmpty()) {
            return filterByProcessStatus(allData, includeFinished);
        }

        // 6. 应用机构过滤逻辑（保持原有逻辑）
        return applyOrgFilterAndProcessStatus(allData, org, includeFinished);
    }

    /**
     * 根据配置查询表单数据（原有方法，保持向后兼容）
     */
    private List<WflowFormData> getFormDataByConfig(WflowFormStatisticsConfig config, boolean includeFinished,
            List<String> org) {
        return getFormDataByConfig(config, includeFinished, org, null);
    }

    /**
     * 应用机构过滤和流程状态过滤
     */
    private List<WflowFormData> applyOrgFilterAndProcessStatus(List<WflowFormData> allData, List<String> org,
            boolean includeFinished) {
        // 获取流程实例ID集合
        Set<String> instanceIds = allData.stream()
                .map(WflowFormData::getInstanceId)
                .collect(Collectors.toSet());

        // 查询历史流程实例
        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(instanceIds)
                .list();

        // 构建用户ID->机构ID的映射 (避免后续嵌套循环)
        Map<Long, Long> userIdToOrgIdMap = instances.stream()
                .map(HistoricProcessInstance::getStartUserId)
                .filter(Objects::nonNull)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toMap(
                        userId -> userId,
                        userId -> usersMapper.selectById(userId).getInstitutionId(),
                        (existing, replacement) -> existing
                ));

        // 构建流程实例ID->机构ID的映射
        Map<String, Long> instanceOrgMap = instances.stream()
                .filter(inst -> userIdToOrgIdMap.containsKey(Long.valueOf(inst.getStartUserId())))
                .collect(Collectors.toMap(
                        HistoricProcessInstance::getId,
                        inst -> userIdToOrgIdMap.get(Long.valueOf(inst.getStartUserId()))
                ));

        // 机构ID白名单
        Set<Long> allowedOrgIds = org.stream().map(Long::valueOf).collect(Collectors.toSet());

        // 过滤原始数据：只保留机构ID在白名单中的数据
        List<WflowFormData> filteredData = allData.stream()
                .filter(data -> {
                    Long orgId = instanceOrgMap.get(data.getInstanceId());
                    return orgId != null && allowedOrgIds.contains(orgId);
                })
                .collect(Collectors.toList());

        return filterByProcessStatus(filteredData, includeFinished);
    }



    /**
     * 获取运行中的流程实例ID
     */
    private Set<String> getRunningInstanceIds() {
        List<ProcessInstance> runningInstances = runtimeService.createProcessInstanceQuery().list();
        return runningInstances.stream()
                .map(ProcessInstance::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 获取流程实例映射
     */
    private Map<String, HistoricProcessInstance> getProcessInstanceMap(Set<String> instanceIds) {
        if (CollectionUtil.isEmpty(instanceIds)) {
            return Collections.emptyMap();
        }

        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(instanceIds)
                .list();

        return instances.stream()
                .collect(Collectors.toMap(HistoricProcessInstance::getId, instance -> instance));
    }

    /**
     * 获取用户名映射
     */
    private Map<String, String> getUserNameMap(Collection<HistoricProcessInstance> instances) {
        Set<String> userIds = instances.stream()
                .map(HistoricProcessInstance::getStartUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        Map<String, String> userNameMap = new HashMap<>();
        for (String userId : userIds) {
            try {
                String userName = orgRepositoryService.getUserById(userId).getUserName();
                userNameMap.put(userId, userName);
            } catch (Exception e) {
                log.warn("获取用户名失败，用户ID: {}", userId, e);
                userNameMap.put(userId, "未知用户");
            }
        }

        return userNameMap;
    }

    /**
     * 过滤匹配的数据
     */
    private List<WflowFormData> filterMatchedData(List<WflowFormData> dataList, String targetValue, String matchType) {
        MatchTypeEnum matchTypeEnum = MatchTypeEnum.getByCode(matchType);

        return dataList.stream()
                .filter(data -> isValueMatched(data.getFieldValue(), targetValue, matchTypeEnum))
                .collect(Collectors.toList());
    }

    /**
     * 判断值是否匹配
     */
    private boolean isValueMatched(String fieldValue, String targetValue, MatchTypeEnum matchType) {
        if (fieldValue == null || targetValue == null) {
            return false;
        }

        switch (matchType) {
            case EXACT:
            case EQUALS:
                return fieldValue.equals(targetValue);
            case CONTAINS:
                return fieldValue.contains(targetValue);
            case REGEX:
                try {
                    return Pattern.matches(targetValue, fieldValue);
                } catch (Exception e) {
                    log.warn("正则表达式匹配失败: {}", targetValue, e);
                    return false;
                }
            case GREATER_THAN:
                return compareNumeric(fieldValue, targetValue) > 0;
            case LESS_THAN:
                return compareNumeric(fieldValue, targetValue) < 0;
            case NOT_EQUALS:
                return !fieldValue.equals(targetValue);
            case RANGE:
                return isInRange(fieldValue, targetValue);
            default:
                return fieldValue.equals(targetValue);
        }
    }

    /**
     * 数值比较
     */
    private int compareNumeric(String value1, String value2) {
        try {
            BigDecimal num1 = new BigDecimal(value1);
            BigDecimal num2 = new BigDecimal(value2);
            return num1.compareTo(num2);
        } catch (NumberFormatException e) {
            // 如果不是数值，则按字符串比较
            return value1.compareTo(value2);
        }
    }

    /**
     * 判断是否在范围内
     */
    private boolean isInRange(String value, String rangeValue) {
        try {
            // 范围格式: "min,max" 或 "min-max"
            String[] parts = rangeValue.contains(",") ? rangeValue.split(",") : rangeValue.split("-");
            if (parts.length != 2) {
                return false;
            }

            BigDecimal num = new BigDecimal(value);
            BigDecimal min = new BigDecimal(parts[0].trim());
            BigDecimal max = new BigDecimal(parts[1].trim());

            return num.compareTo(min) >= 0 && num.compareTo(max) <= 0;
        } catch (Exception e) {
            log.warn("范围匹配失败: value={}, range={}", value, rangeValue, e);
            return false;
        }
    }

    /**
     * 构建匹配的实例列表
     */
    private List<FormStatisticsVo.MatchedInstance> buildMatchedInstances(
            List<WflowFormData> matchedData,
            Map<String, HistoricProcessInstance> instanceMap,
            Map<String, String> userNameMap) {

        return matchedData.stream().<FormStatisticsVo.MatchedInstance>map(data -> {
                    HistoricProcessInstance instance = instanceMap.get(data.getInstanceId());
                    if (instance == null) {
                        return null;
                    }

                    String startUserId = instance.getStartUserId();
                    String startUserName = userNameMap.getOrDefault(startUserId, "未知用户");

                    LocalDateTime createTime = null;
                    if (data.getCreateTime() != null) {
                        createTime = data.getCreateTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();
                    }

                    return FormStatisticsVo.MatchedInstance.builder()
                            .instanceId(data.getInstanceId())
                            .instanceName(instance.getName())
                            .startUser(startUserId)
                            .startUserName(startUserName)
                            .fieldValue(data.getFieldValue())
                            .createTime(createTime)
                            .status(instance.getEndTime() != null ? "已完成" : "运行中")
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 计算匹配率
     */
    private BigDecimal calculateMatchRate(int matchCount, int totalCount) {
        if (totalCount == 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(matchCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculateFieldSum(String formId, String fieldId, boolean includeFinished) {
        return calculateFieldSumWithOrgFilter(formId, fieldId, includeFinished, null);
    }

    @Override
    public BigDecimal calculateFieldSumWithOrgFilter(String formId, String fieldId, boolean includeFinished,
            List<String> orgIds) {
        return calculateFieldSumWithOrgFilter(formId, fieldId, includeFinished, orgIds, null);
    }

    /**
     * 计算字段求和（支持机构过滤和filters过滤）
     */
    @Override
    public BigDecimal calculateFieldSumWithOrgFilter(String formId, String fieldId, boolean includeFinished,
            List<String> orgIds, Map<String, Object> filters) {
        if (StrUtil.isBlank(formId) || StrUtil.isBlank(fieldId)) {
            log.warn("表单ID或字段ID不能为空");
            return BigDecimal.ZERO;
        }

        try {
            // 查询表单数据
            List<WflowFormData> formDataList = getFormDataForSum(formId, fieldId, includeFinished, orgIds, filters);

            if (CollectionUtil.isEmpty(formDataList)) {
                log.info("未找到表单数据，表单ID: {}, 字段ID: {}", formId, fieldId);
                return BigDecimal.ZERO;
            }

            // 计算数值总和
            BigDecimal sum = BigDecimal.ZERO;
            int validCount = 0;
            int invalidCount = 0;

            for (WflowFormData data : formDataList) {
                String fieldValue = data.getFieldValue();
                if (StrUtil.isBlank(fieldValue)) {
                    continue;
                }

                try {
                    // 尝试将字段值转换为数值
                    BigDecimal value = new BigDecimal(fieldValue.trim());
                    sum = sum.add(value);
                    validCount++;
                } catch (NumberFormatException e) {
                    invalidCount++;
                    log.debug("字段值无法转换为数值，实例ID: {}, 字段值: {}", data.getInstanceId(), fieldValue);
                }
            }

            log.info("字段求和完成，表单ID: {}, 字段ID: {}, 总和: {}, 有效数据: {}, 无效数据: {}",
                    formId, fieldId, sum, validCount, invalidCount);

            return sum;
        } catch (Exception e) {
            log.error("计算字段求和失败，表单ID: {}, 字段ID: {}", formId, fieldId, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public List<WflowFormStatisticsConfig> getStatisticsConfigsByType(String type) {
        return statisticsConfigMapper.selectList(new LambdaQueryWrapper<WflowFormStatisticsConfig>()
                .eq(WflowFormStatisticsConfig::getConfigName, type)
                .eq(WflowFormStatisticsConfig::getStatus, 1));
    }

    @Override
    public WflowFormStatisticsConfig getStatisticsConfigsById(String id) {
        return statisticsConfigMapper.selectOne(new LambdaQueryWrapper<WflowFormStatisticsConfig>()
                .eq(WflowFormStatisticsConfig::getId, id));
    }

    /**
     * 查询用于求和计算的表单数据（支持filters过滤）
     */
    private List<WflowFormData> getFormDataForSum(String formId, String fieldId, boolean includeFinished,
            List<String> orgIds, Map<String, Object> filters) {

        // 1. 首先根据filters获取符合条件的实例ID集合
        Set<String> filteredInstanceIds = null;
        if (filters != null && !filters.isEmpty()) {
            // 提取自定义字段过滤条件（排除传统参数）
            Map<String, Object> customFilters = new HashMap<>(filters);

            if (!customFilters.isEmpty()) {
                filteredInstanceIds = buildFormDataFilterConditions(customFilters);
                log.debug("根据自定义字段过滤得到实例ID数量: {}",
                        filteredInstanceIds != null ? filteredInstanceIds.size() : 0);
            }
        }

        // 2. 构建基础查询条件
        LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormData::getCode, formId)
                .eq(WflowFormData::getFieldId, fieldId)
                .isNotNull(WflowFormData::getFieldValue)
                .ne(WflowFormData::getFieldValue, "");

        // 3. 应用实例ID过滤条件
        if (filteredInstanceIds != null && !filteredInstanceIds.isEmpty()) {
            wrapper.in(WflowFormData::getInstanceId, filteredInstanceIds);
        } else if (filteredInstanceIds != null && filteredInstanceIds.isEmpty()) {
            // 如果过滤条件存在但没有匹配的实例，直接返回空列表
            log.debug("过滤条件没有匹配到任何实例，返回空结果");
            return Collections.emptyList();
        }

        // 查询所有原始数据
        List<WflowFormData> allData = formDataMapper.selectList(wrapper);

        if (CollectionUtil.isEmpty(allData)) {
            return Collections.emptyList();
        }

        // 如果没有指定机构过滤，则根据当前用户权限自动获取机构范围
        List<String> finalOrgIds = orgIds;
        if (finalOrgIds == null) {
            finalOrgIds = getCurrentUserOrgIds();
        }

        // 如果机构ID列表为空，返回所有数据（超级管理员权限）
        if (CollectionUtil.isEmpty(finalOrgIds)) {
            return filterByProcessStatus(allData, includeFinished);
        }

        // 按机构权限过滤数据
        List<WflowFormData> filteredData = filterDataByOrg(allData, finalOrgIds);

        // 按流程状态过滤
        return filterByProcessStatus(filteredData, includeFinished);
    }

    /**
     * 构建WflowFormData的查询条件，用于过滤表单数据
     */
    private Set<String> buildFormDataFilterConditions(Map<String, Object> filters) {
        if (filters == null || filters.isEmpty()) {
            log.debug("没有过滤条件，跳过表单数据过滤");
            return null;
        }

        log.debug("开始构建表单数据过滤条件，过滤字段数: {}", filters.size());

        // 存储每个过滤条件匹配的实例ID集合
        List<Set<String>> filterResults = new ArrayList<>();

        for (Map.Entry<String, Object> filter : filters.entrySet()) {
            String fieldId = filter.getKey();
            Object fieldValue = filter.getValue();

            if (fieldValue == null) {
                log.debug("跳过空值过滤条件，字段ID: {}", fieldId);
                continue;
            }

            Set<String> matchingInstanceIds = getInstanceIdsByFieldFilter(fieldId, fieldValue.toString());
            filterResults.add(matchingInstanceIds);

            log.debug("字段过滤结果 - 字段ID: {}, 字段值: {}, 匹配实例数: {}",
                    fieldId, fieldValue, matchingInstanceIds.size());
        }

        if (filterResults.isEmpty()) {
            log.debug("所有过滤条件都为空，跳过过滤");
            return null;
        }

        // 计算所有过滤条件的交集（AND逻辑）
        Set<String> finalInstanceIds = filterResults.get(0);
        for (int i = 1; i < filterResults.size(); i++) {
            finalInstanceIds = finalInstanceIds.stream()
                    .filter(filterResults.get(i)::contains)
                    .collect(Collectors.toSet());
        }

        log.debug("过滤条件AND运算结果，最终匹配实例数: {}", finalInstanceIds.size());

        return finalInstanceIds;
    }

    /**
     * 根据字段ID和字段值查询匹配的实例ID集合
     */
    private Set<String> getInstanceIdsByFieldFilter(String fieldId, String fieldValue) {
        try {
            LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WflowFormData::getFieldId, fieldId)
                    .eq(WflowFormData::getFieldValue, fieldValue);

            List<WflowFormData> matchingData = formDataMapper.selectList(wrapper);

            return matchingData.stream()
                    .map(WflowFormData::getInstanceId)
                    .collect(Collectors.toSet());

        } catch (Exception e) {
            log.error("查询字段过滤数据失败，字段ID: {}, 字段值: {}", fieldId, fieldValue, e);
            return new HashSet<>();
        }
    }


    /**
     * 获取当前用户的机构权限范围
     */
    private List<String> getCurrentUserOrgIds() {
        try {
            String userId = UserUtil.getLoginUserId();
            User user = usersMapper.selectById(Long.valueOf(userId));

            // 查询用户角色，判断是否为超级管理员
            List<WflowRoles> roles = rolesMapper.getRole(userId, "1729671487141314562");

            if (CollectionUtil.isEmpty(roles)) {
                // 普通用户，查询机构和子机构
                return orgRepositoryService.getRecursiveSubInstitution(String.valueOf(user.getInstitutionId()));
            } else {
                // 超级管理员，返回空列表表示不限制机构
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("获取当前用户机构权限失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 按机构权限过滤数据
     */
    private List<WflowFormData> filterDataByOrg(List<WflowFormData> allData, List<String> orgIds) {
        // 获取流程实例ID集合
        Set<String> instanceIds = allData.stream()
                .map(WflowFormData::getInstanceId)
                .collect(Collectors.toSet());

        if (CollectionUtil.isEmpty(instanceIds)) {
            return Collections.emptyList();
        }

        // 获取流程实例信息
        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(instanceIds)
                .list();

        // 获取用户ID到机构ID的映射
        Set<String> userIds = instances.stream()
                .map(HistoricProcessInstance::getStartUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, Long> userIdToOrgIdMap = new HashMap<>();
        for (String userId : userIds) {
            try {
                User user = usersMapper.selectById(Long.valueOf(userId));
                if (user != null && user.getInstitutionId() != null) {
                    userIdToOrgIdMap.put(Long.valueOf(userId), user.getInstitutionId());
                }
            } catch (Exception e) {
                log.warn("获取用户机构信息失败，用户ID: {}", userId, e);
            }
        }

        // 构建流程实例ID->机构ID的映射
        Map<String, Long> instanceOrgMap = instances.stream()
                .filter(inst -> userIdToOrgIdMap.containsKey(Long.valueOf(inst.getStartUserId())))
                .collect(Collectors.toMap(
                        HistoricProcessInstance::getId,
                        inst -> userIdToOrgIdMap.get(Long.valueOf(inst.getStartUserId()))));

        // 机构ID白名单
        Set<Long> allowedOrgIds = orgIds.stream().map(Long::valueOf).collect(Collectors.toSet());

        // 过滤数据：只保留机构ID在白名单中的数据
        return allData.stream()
                .filter(data -> {
                    Long orgId = instanceOrgMap.get(data.getInstanceId());
                    return orgId != null && allowedOrgIds.contains(orgId);
                })
                .collect(Collectors.toList());
    }

    /**
     * 按流程状态过滤数据
     */
    private List<WflowFormData> filterByProcessStatus(List<WflowFormData> data, boolean includeFinished) {
        if (includeFinished) {
            return data;
        }

        // 只包含运行中的流程
        Set<String> runningInstanceIds = getRunningInstanceIds();
        return data.stream()
                .filter(formData -> runningInstanceIds.contains(formData.getInstanceId()))
                .collect(Collectors.toList());
    }

    @Override
    public Page<LandAcquisitionStatisticsVo> getLandAcquisitionStatistics(String xzqCode, Map<String, Object> filters,
            String formName, String configName,
            Integer pageNo, Integer pageSize, String sortField, String sortOrder) {
        log.info("开始执行征地统计，行政区代码: {}, 过滤条件: {}, 表单名称: {}, 配置名称: {}, 排序字段: {}, 排序方向: {}",
                xzqCode, filters, formName, configName, sortField, sortOrder);

        // 参数校验和默认值设置
        if (StrUtil.isBlank(xzqCode)) {
            xzqCode = "140000000000";
        }
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (StrUtil.isBlank(sortField)) {
            sortField = "xzqName";
        }
        if (StrUtil.isBlank(sortOrder)) {
            sortOrder = "ASC";
        }

        try {
            // 1. 构建缓存键
            String cacheKey = buildCacheKey(xzqCode, filters, formName, configName);

            // 2. 尝试从缓存获取数据
            List<LandAcquisitionStatisticsVo> resultList = cacheService.getList(cacheKey,
                    LandAcquisitionStatisticsVo.class);

            if (resultList == null) {
                // 3. 缓存未命中，查询数据库
                resultList = queryFromDatabase(xzqCode, filters, formName, configName);

                // 4. 将结果存入缓存（30分钟过期）
                if (resultList != null && !resultList.isEmpty()) {
                    cacheService.set(cacheKey, resultList, 30, TimeUnit.MINUTES);
                }
            } else {
                log.debug("从缓存获取征地统计数据，缓存键: {}", cacheKey);
            }

            if (resultList == null) {
                resultList = new ArrayList<>();
            }

            // 5. 排序处理
            resultList = sortStatisticsResults(resultList, sortField, sortOrder);

            // 6. 手动分页处理
            int total = resultList.size();
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, total);

            List<LandAcquisitionStatisticsVo> pagedList = new ArrayList<>();
            if (start < total) {
                pagedList = resultList.subList(start, end);
            }

            // 7. 构建分页结果
            Page<LandAcquisitionStatisticsVo> page = new Page<>(pageNo, pageSize);
            page.setRecords(pagedList);
            page.setTotal(total);

            log.info("征地统计完成，共查询到 {} 条记录", total);
            return page;

        } catch (Exception e) {
            log.error("征地统计执行失败", e);
            throw new RuntimeException("征地统计执行失败: " + e.getMessage());
        }
    }

    @Override
    public String clearLandAcquisitionStatisticsCache(String xzqCode, Map<String, Object> filters, String formName,
            String configName) {
        try {
            if (xzqCode != null || filters != null || formName != null || configName != null) {
                // 清除指定条件的缓存（这里landStatusFieldId设为null，因为缓存清除时不需要具体的字段ID）
                String cacheKey = buildCacheKey(xzqCode, filters, formName, configName);
                cacheService.delete(cacheKey);
                log.info("清除指定征地统计缓存成功，缓存键: {}", cacheKey);
                return "清除指定缓存成功";
            } else {
                // 清除所有征地统计缓存
                cacheService.deleteByPattern("land_statistics:*");
                log.info("清除所有征地统计缓存成功");
                return "清除所有缓存成功";
            }
        } catch (Exception e) {
            log.error("清除征地统计缓存失败", e);
            return "清除缓存失败: " + e.getMessage();
        }
    }

    /**
     * 优化：构建分层缓存键
     */
    private String buildCacheKey(String xzqCode, Map<String, Object> filters, String formName, String configName) {
        StringBuilder keyBuilder = new StringBuilder("land_statistics:");

        // 添加版本号，便于缓存失效管理
        keyBuilder.append("v3:");

        // 按重要性排序缓存键组件，提高缓存命中率
        keyBuilder.append(xzqCode != null ? xzqCode : "null").append(":");

        // 处理filters参数，确保缓存键的一致性
        if (filters != null && !filters.isEmpty()) {
            // 对filters的key进行排序，确保相同过滤条件生成相同的缓存键
            filters.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> keyBuilder.append(entry.getKey()).append("=")
                            .append(entry.getValue() != null ? entry.getValue().toString() : "null").append(";"));
        } else {
            keyBuilder.append("no_filters:");
        }

        keyBuilder.append(formName != null ? formName : "null").append(":");
        keyBuilder.append(configName != null ? configName : "null");

        return keyBuilder.toString();
    }


    /**
     * 构建行政区机构缓存键
     */
    private String buildOrgCacheKey(String xzqCode) {
        return "org_ids:v1:" + xzqCode;
    }

    /**
     * 构建行政区下级缓存键
     */
    private String buildSubRegionCacheKey(Long parentId) {
        return "sub_regions:v1:" + parentId;
    }

    /**
     * 从数据库查询征地统计数据
     */
    private List<LandAcquisitionStatisticsVo> queryFromDatabase(String xzqCode, Map<String, Object> filters,
            String formName, String configName) {
        // 1. 获取当前行政区信息
        SysXzq14 currentXzq = sysXzq14Mapper.selectOne(
                Wrappers.<SysXzq14>lambdaQuery().eq(SysXzq14::getCode, xzqCode));

        if (currentXzq == null) {
            log.warn("未找到行政区代码: {}", xzqCode);
            return new ArrayList<>();
        }

        // 2. 根据当前行政区级别确定查询策略
        List<LandAcquisitionStatisticsVo> resultList;

        if (currentXzq.getLevel() <= 2) {
            // 省级或市级：查询下级行政区汇总统计
            resultList = getSubRegionStatistics(currentXzq, filters, formName, configName);
        } else {
            // 县级及以下：查询详细表单统计
            resultList = getDetailedFormStatistics(currentXzq, filters, formName, configName);
        }

        return resultList != null ? resultList : new ArrayList<>();
    }

    /**
     * 对统计结果进行排序
     */
    private List<LandAcquisitionStatisticsVo> sortStatisticsResults(List<LandAcquisitionStatisticsVo> resultList,
            String sortField, String sortOrder) {
        if (resultList == null || resultList.isEmpty()) {
            return resultList;
        }

        // 如果只有一条记录或者没有指定排序字段，直接返回
        if (resultList.size() <= 1 || StrUtil.isBlank(sortField)) {
            return resultList;
        }

        boolean isAsc = "ASC".equalsIgnoreCase(sortOrder);

        // 类型安全检查：确保第一个元素是正确的类型
        Object firstElement = resultList.get(0);
        LandAcquisitionStatisticsVo summaryRecord;

        if (firstElement instanceof LandAcquisitionStatisticsVo) {
            summaryRecord = (LandAcquisitionStatisticsVo) firstElement;
        } else {
            // 如果类型不匹配，尝试使用安全转换
            summaryRecord = TaskResultSerializer.safeCast(firstElement, LandAcquisitionStatisticsVo.class);
            if (summaryRecord == null) {
                log.warn("第一条记录类型转换失败，实际类型: {}", firstElement.getClass().getName());
                // 创建一个空的汇总记录作为降级处理
                summaryRecord = new LandAcquisitionStatisticsVo();
            }
        }

        List<LandAcquisitionStatisticsVo> otherRecords = new ArrayList<>();

        // 安全地处理其他记录
        for (int i = 1; i < resultList.size(); i++) {
            Object element = resultList.get(i);
            if (element instanceof LandAcquisitionStatisticsVo) {
                otherRecords.add((LandAcquisitionStatisticsVo) element);
            } else {
                LandAcquisitionStatisticsVo converted = TaskResultSerializer.safeCast(element,
                        LandAcquisitionStatisticsVo.class);
                if (converted != null) {
                    otherRecords.add(converted);
                } else {
                    log.warn("记录类型转换失败，跳过该记录，实际类型: {}", element.getClass().getName());
                }
            }
        }

        // 只对其他记录进行排序，汇总记录始终保持在第一位
        List<LandAcquisitionStatisticsVo> sortedOtherRecords = otherRecords.stream()
                .sorted((a, b) -> {
                    Object valueA = getFieldValue(a, sortField);
                    Object valueB = getFieldValue(b, sortField);

                    if (valueA == null && valueB == null) {
                        return 0;
                    }
                    if (valueA == null) {
                        return isAsc ? -1 : 1;
                    }
                    if (valueB == null) {
                        return isAsc ? 1 : -1;
                    }

                    int result = compareValues(valueA, valueB);
                    return isAsc ? result : -result;
                })
                .collect(Collectors.toList());

        // 重新组装结果：汇总记录 + 排序后的其他记录
        List<LandAcquisitionStatisticsVo> finalResult = new ArrayList<>();
        finalResult.add(summaryRecord);
        finalResult.addAll(sortedOtherRecords);

        return finalResult;
    }

    /**
     * 比较两个值
     */
    @SuppressWarnings("unchecked")
    private int compareValues(Object valueA, Object valueB) {
        if (valueA instanceof Comparable && valueB instanceof Comparable) {
            try {
                return ((Comparable<Object>) valueA).compareTo(valueB);
            } catch (ClassCastException e) {
                // 如果类型不匹配，转换为字符串比较
                return valueA.toString().compareTo(valueB.toString());
            }
        }
        return valueA.toString().compareTo(valueB.toString());
    }

    /**
     * 获取字段值用于排序
     */
    private Object getFieldValue(LandAcquisitionStatisticsVo vo, String fieldName) {
        try {
            switch (fieldName) {
                case "xzqCode":
                    return vo.getXzqCode();
                case "xzqName":
                    return vo.getXzqName();
                case "xzqLevel":
                    return vo.getXzqLevel();
                case "approvedLandArea":
                    return vo.getApprovedLandArea();
                case "approvedLandCost":
                    return vo.getApprovedLandCost();
                case "approvedAgriculturalPopulation":
                    return vo.getApprovedAgriculturalPopulation();
                case "verifiedLandArea":
                    return vo.getVerifiedLandArea();
                case "verifiedLandCost":
                    return vo.getVerifiedLandCost();
                case "verifiedAgriculturalPopulation":
                    return vo.getVerifiedAgriculturalPopulation();
                case "actualCompletedLandArea":
                    return vo.getActualCompletedLandArea();
                case "actualCompletedLandCost":
                    return vo.getActualCompletedLandCost();
                case "actualCompletedAgriculturalPopulation":
                    return vo.getActualCompletedAgriculturalPopulation();
                case "recordCount":
                    return vo.getRecordCount();
                default:
                    return vo.getXzqName(); // 默认按行政区名称排序
            }
        } catch (Exception e) {
            log.warn("获取排序字段值失败，字段: {}", fieldName, e);
            return vo.getXzqName();
        }
    }

    /**
     * 获取下级行政区汇总统计
     */
    private List<LandAcquisitionStatisticsVo> getSubRegionStatistics(SysXzq14 currentXzq, Map<String, Object> filters,
            String formName, String configName) {
        List<LandAcquisitionStatisticsVo> resultList = new ArrayList<>();

        // 首先添加当前行政区的汇总统计（包含所有下级行政区的数据）
        LandAcquisitionStatisticsVo summaryStatistics = calculateCurrentRegionSummary(
                currentXzq, filters, formName, configName);
        resultList.add(summaryStatistics);

        // 查询下级行政区
        List<SysXzq14> subRegions = sysXzq14Mapper.selectList(
                Wrappers.<SysXzq14>lambdaQuery()
                        .eq(SysXzq14::getPId, currentXzq.getId())
                        .orderBy(true, true, SysXzq14::getSort));

        // 并行处理各下级行政区的独立统计
        List<LandAcquisitionStatisticsVo> subRegionStatistics = subRegions.parallelStream()
                .map(subRegion -> {
                    try {
                        // 获取该行政区下的所有机构
                        List<String> orgIds = getOrgIdsByXzqCode(subRegion.getCode());

                        if (CollectionUtil.isEmpty(orgIds)) {
                            // 如果没有机构，创建空统计记录
                            return createEmptyStatisticsVo(subRegion);
                        }

                        // 执行统计计算
                        return calculateLandStatistics(
                                subRegion, orgIds, filters, formName, configName);
                    } catch (Exception e) {
                        log.error("计算下级行政区统计失败，行政区: {}", subRegion.getName(), e);
                        return createEmptyStatisticsVo(subRegion);
                    }
                })
                .collect(Collectors.toList());

        resultList.addAll(subRegionStatistics);

        return resultList;
    }

    /**
     * 计算当前行政区的汇总统计（包含所有下级行政区的数据）
     */
    private LandAcquisitionStatisticsVo calculateCurrentRegionSummary(SysXzq14 currentXzq, Map<String, Object> filters,
            String formName, String configName) {
        try {
            // 1. 获取当前行政区及其所有下级行政区的机构ID
            List<String> allOrgIds = getAllOrgIdsByXzqCode(currentXzq.getCode());

            if (CollectionUtil.isEmpty(allOrgIds)) {
                log.warn("未找到行政区下的机构，行政区: {}", currentXzq.getName());
                return createEmptyStatisticsVo(currentXzq);
            }

            // 2. 执行汇总统计计算
            LandAcquisitionStatisticsVo summaryStatistics = calculateLandStatistics(
                    currentXzq, allOrgIds, filters, formName, configName);

            return summaryStatistics;

        } catch (Exception e) {
            log.error("计算当前行政区汇总统计失败，行政区: {}", currentXzq.getName(), e);
            return createEmptyStatisticsVo(currentXzq);
        }
    }

    /**
     * 优化：批量获取当前行政区及其所有下级行政区的机构ID列表（添加缓存）
     */
    private List<String> getAllOrgIdsByXzqCode(String xzqCode) {
        try {
            // 1. 尝试从缓存获取
            String cacheKey = buildOrgCacheKey(xzqCode);
            List<String> cachedOrgIds = cacheService.getList(cacheKey, String.class);
            if (cachedOrgIds != null) {
                log.debug("从缓存获取机构ID列表，行政区: {}", xzqCode);
                return cachedOrgIds;
            }

            // 2. 获取当前行政区信息
            SysXzq14 currentXzq = sysXzq14Mapper.selectOne(
                    Wrappers.<SysXzq14>lambdaQuery().eq(SysXzq14::getCode, xzqCode));

            if (currentXzq == null) {
                return new ArrayList<>();
            }

            // 3. 获取所有下级行政区（优化后的单次查询）
            List<SysXzq14> allSubRegions = getAllSubRegions(currentXzq.getId());

            // 4. 包含当前行政区
            allSubRegions.add(currentXzq);

            // 5. 批量获取所有行政区的机构ID（优化：减少查询次数）
            Set<String> allOrgIds = new HashSet<>();

            // 6. 使用并行流处理，提高性能
            allSubRegions.parallelStream().forEach(xzq -> {
                List<String> orgIds = getOrgIdsByXzqCode(xzq.getCode());
                synchronized (allOrgIds) {
                    allOrgIds.addAll(orgIds);
                }
            });

            List<String> result = new ArrayList<>(allOrgIds);

            // 7. 存入缓存（1小时过期）
            cacheService.set(cacheKey, result, 1, TimeUnit.HOURS);

            return result;

        } catch (Exception e) {
            log.error("获取行政区机构ID列表失败，行政区代码: {}", xzqCode, e);
            return new ArrayList<>();
        }
    }

    /**
     * 优化：使用单次查询获取所有下级行政区（基于层级路径，添加缓存）
     */
    private List<SysXzq14> getAllSubRegions(Long parentId) {
        try {
            // 1. 尝试从缓存获取
            String cacheKey = buildSubRegionCacheKey(parentId);
            List<SysXzq14> cachedSubRegions = cacheService.getList(cacheKey, SysXzq14.class);
            if (cachedSubRegions != null) {
                log.debug("从缓存获取下级行政区，父级ID: {}", parentId);
                return cachedSubRegions;
            }

            // 2. 先获取父级行政区信息
            SysXzq14 parentXzq = sysXzq14Mapper.selectById(parentId);
            if (parentXzq == null) {
                return new ArrayList<>();
            }

            // 3. 使用层级路径一次性查询所有下级行政区
            // 假设行政区编码有层级关系，如：140000000000(省) -> 140100000000(市) -> 140101000000(区)
            String parentCode = parentXzq.getCode();

            // 4. 根据编码规则查询所有下级行政区
            List<SysXzq14> allSubRegions = sysXzq14Mapper.selectList(
                    Wrappers.<SysXzq14>lambdaQuery()
                            .like(SysXzq14::getCode, parentCode.substring(0, getCodePrefixLength(parentCode)))
                            .ne(SysXzq14::getId, parentId)
                            .gt(SysXzq14::getLevel, parentXzq.getLevel())
                            .orderBy(true, true, SysXzq14::getLevel, SysXzq14::getSort));

            // 5. 存入缓存（2小时过期，行政区变化较少）
            cacheService.set(cacheKey, allSubRegions, 2, TimeUnit.HOURS);

            return allSubRegions;

        } catch (Exception e) {
            log.error("获取所有下级行政区失败，父级ID: {}", parentId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据行政区编码获取前缀长度
     */
    private int getCodePrefixLength(String code) {
        if (code.length() >= 12) {
            // 根据行政区级别确定前缀长度
            if (code.endsWith("0000000000")) {
                return 2;
            }
            if (code.endsWith("00000000")) {
                return 4;
            }
            if (code.endsWith("000000")) {
                return 6;
            }
            return 9;
        }
        return code.length();
    }

    /**
     * 获取详细表单统计（县级及以下）
     */
    private List<LandAcquisitionStatisticsVo> getDetailedFormStatistics(SysXzq14 currentXzq,
            Map<String, Object> filters,
            String formName, String configName) {
        List<LandAcquisitionStatisticsVo> resultList = new ArrayList<>();

        // 获取当前行政区下的所有机构
        List<String> orgIds = getOrgIdsByXzqCode(currentXzq.getCode());

        if (CollectionUtil.isEmpty(orgIds)) {
            // 如果没有机构，返回空统计
            LandAcquisitionStatisticsVo vo = createEmptyStatisticsVo(currentXzq);
            resultList.add(vo);
            return resultList;
        }

        // 执行详细统计
        LandAcquisitionStatisticsVo statistics = calculateLandStatistics(
                currentXzq, orgIds, filters, formName, configName);
        resultList.add(statistics);

        return resultList;
    }

    /**
     * 根据行政区代码获取机构ID列表
     */
    private List<String> getOrgIdsByXzqCode(String xzqCode) {
        return institutionMapper.selectList(
                Wrappers.<SafetyInstitution>lambdaQuery()
                        .eq(SafetyInstitution::getXzqCode, xzqCode))
                .stream()
                .flatMap(safetyInstitution -> {
                    // 获取父机构ID
                    String parentId = String.valueOf(safetyInstitution.getId());
                    // 获取所有递归子机构
                    List<String> subInstitutions = orgRepositoryService.getRecursiveSubInstitution(parentId);
                    // 创建包含父机构+所有子机构的流
                    return Stream.concat(
                            Stream.of(parentId), // 父机构ID作为第一个元素
                            subInstitutions.stream() // 所有子机构流
                    );
                }).distinct()
                .collect(Collectors.toList());
    }

    /**
     * 创建空的统计VO对象
     */
    private LandAcquisitionStatisticsVo createEmptyStatisticsVo(SysXzq14 xzq) {
        // 检查是否有下级行政区
        long subRegionCount = sysXzq14Mapper.selectCount(
                Wrappers.<SysXzq14>lambdaQuery().eq(SysXzq14::getPId, xzq.getId()));

        return LandAcquisitionStatisticsVo.builder()
                .xzqCode(xzq.getCode())
                .xzqName(xzq.getName())
                .xzqLevel(xzq.getLevel())
                .approvedLandArea(BigDecimal.ZERO)
                .approvedLandCost(BigDecimal.ZERO)
                .approvedAgriculturalPopulation(BigDecimal.ZERO)
                .verifiedLandArea(BigDecimal.ZERO)
                .verifiedLandCost(BigDecimal.ZERO)
                .verifiedAgriculturalPopulation(BigDecimal.ZERO)
                .actualCompletedLandArea(BigDecimal.ZERO)
                .actualCompletedLandCost(BigDecimal.ZERO)
                .actualCompletedAgriculturalPopulation(BigDecimal.ZERO)
                .recordCount(0)
                .hasSubRegions(subRegionCount > 0)
                .build();
    }

    /**
     * 计算征地统计数据
     */
    private LandAcquisitionStatisticsVo calculateLandStatistics(SysXzq14 xzq, List<String> orgIds,
            Map<String, Object> filters, String formName, String configName) {
        try {
            // 1. 查询统计配置
            List<WflowFormStatisticsConfig> configs = getStatisticsConfigsByFormAndConfig(formName, configName);

            if (CollectionUtil.isEmpty(configs)) {
                log.warn("未找到匹配的统计配置，表单名称: {}, 配置名称: {}", formName, configName);
                return createEmptyStatisticsVo(xzq);
            }

            // 2. 初始化统计结果
            LandAcquisitionStatisticsVo.LandAcquisitionStatisticsVoBuilder builder = LandAcquisitionStatisticsVo
                    .builder()
                    .xzqCode(xzq.getCode())
                    .xzqName(xzq.getName())
                    .xzqLevel(xzq.getLevel())
                    .approvedLandArea(BigDecimal.ZERO)
                    .approvedLandCost(BigDecimal.ZERO)
                    .approvedAgriculturalPopulation(BigDecimal.ZERO)
                    .verifiedLandArea(BigDecimal.ZERO)
                    .verifiedLandCost(BigDecimal.ZERO)
                    .verifiedAgriculturalPopulation(BigDecimal.ZERO)
                    .actualCompletedLandArea(BigDecimal.ZERO)
                    .actualCompletedLandCost(BigDecimal.ZERO)
                    .actualCompletedAgriculturalPopulation(BigDecimal.ZERO)
                    .recordCount(0);

            // 3. 检查是否有下级行政区
            long subRegionCount = sysXzq14Mapper.selectCount(
                    Wrappers.<SysXzq14>lambdaQuery().eq(SysXzq14::getPId, xzq.getId()));
            builder.hasSubRegions(subRegionCount > 0);

            // 4. 遍历配置进行统计计算
            int totalRecords = 0;
            for (WflowFormStatisticsConfig config : configs) {
                BigDecimal sum = calculateFieldSumWithOrgFilter(
                        config.getFormId(), config.getFieldId(), true, orgIds, filters);

                // 根据字段名称分类累加
                String fieldName = config.getFieldName();
                if (fieldName != null) {
                    if (fieldName.contains("批准") && fieldName.contains("面积")) {
                        builder.approvedLandArea(sum);
                    } else if (fieldName.contains("批准") && fieldName.contains("费用")) {
                        builder.approvedLandCost(sum);
                    } else if (fieldName.contains("批准") && fieldName.contains("人口")) {
                        builder.approvedAgriculturalPopulation(sum);
                    } else if (fieldName.contains("核定") && fieldName.contains("面积")) {
                        builder.verifiedLandArea(sum);
                    } else if (fieldName.contains("核定") && fieldName.contains("费用")) {
                        builder.verifiedLandCost(sum);
                    } else if (fieldName.contains("核定") && fieldName.contains("人口")) {
                        builder.verifiedAgriculturalPopulation(sum);
                    } else if (fieldName.contains("实际") && fieldName.contains("面积")) {
                        builder.actualCompletedLandArea(sum);
                    } else if (fieldName.contains("实际") && fieldName.contains("费用")) {
                        builder.actualCompletedLandCost(sum);
                    } else if (fieldName.contains("实际") && fieldName.contains("人口")) {
                        builder.actualCompletedAgriculturalPopulation(sum);
                    }
                }

                // 统计记录数（使用第一个配置的记录数作为总记录数）
                if (totalRecords == 0) {
                    totalRecords = getRecordCount(config, orgIds, filters);
                }
            }

            builder.recordCount(totalRecords);
            return builder.build();

        } catch (Exception e) {
            log.error("计算征地统计数据失败，行政区: {}", xzq.getName(), e);
            return createEmptyStatisticsVo(xzq);
        }
    }

    /**
     * 根据表单名称和配置名称查询统计配置
     */
    private List<WflowFormStatisticsConfig> getStatisticsConfigsByFormAndConfig(String formName, String configName) {
        LambdaQueryWrapper<WflowFormStatisticsConfig> wrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(configName)) {
            wrapper.eq(WflowFormStatisticsConfig::getConfigName, configName);
        }

        if (StrUtil.isNotBlank(formName)) {
            // 通过表单名称查询表单ID，然后查询配置
            List<WflowModels> models = modelsMapper.selectList(
                    Wrappers.<WflowModels>lambdaQuery()
                            .eq(WflowModels::getFormName, formName));

            if (CollectionUtil.isNotEmpty(models)) {
                List<String> formIds = models.stream()
                        .map(WflowModels::getFormId)
                        .collect(Collectors.toList());
                wrapper.in(WflowFormStatisticsConfig::getFormId, formIds);
            } else {
                // 如果没找到对应的表单，返回空列表
                return new ArrayList<>();
            }
        }

        wrapper.eq(WflowFormStatisticsConfig::getStatus, 1);

        return statisticsConfigMapper.selectList(wrapper);
    }

    /**
     * 获取记录数量（支持filters过滤）
     */
    private int getRecordCount(WflowFormStatisticsConfig config, List<String> orgIds, Map<String, Object> filters) {
        try {
            // 1. 首先根据filters获取符合条件的实例ID集合
            Set<String> filteredInstanceIds = null;
            if (filters != null && !filters.isEmpty()) {
                // 提取自定义字段过滤条件（排除传统参数）
                Map<String, Object> customFilters = new HashMap<>(filters);
                if (!customFilters.isEmpty()) {
                    filteredInstanceIds = buildFormDataFilterConditions(customFilters);
                    log.debug("根据自定义字段过滤得到实例ID数量: {}",
                            filteredInstanceIds != null ? filteredInstanceIds.size() : 0);
                }
            }

            // 2. 构建基础查询条件
            LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WflowFormData::getCode, config.getFormId())
                    .eq(WflowFormData::getFieldId, config.getFieldId())
                    .isNotNull(WflowFormData::getFieldValue)
                    .ne(WflowFormData::getFieldValue, "");

            // 3. 应用实例ID过滤条件
            if (filteredInstanceIds != null && !filteredInstanceIds.isEmpty()) {
                wrapper.in(WflowFormData::getInstanceId, filteredInstanceIds);
            } else if (filteredInstanceIds != null && filteredInstanceIds.isEmpty()) {
                // 如果过滤条件存在但没有匹配的实例，直接返回0
                log.debug("过滤条件没有匹配到任何实例，返回0");
                return 0;
            }

            // 4. 执行查询
            List<WflowFormData> formDataList = formDataMapper.selectList(wrapper);

            // 5. 如果没有机构过滤条件，直接返回结果
            if (orgIds == null || orgIds.isEmpty()) {
                return formDataList.size();
            }

            // 6. 应用机构过滤逻辑
            List<WflowFormData> filteredData = applyOrgFilterAndProcessStatus(formDataList, orgIds, true);

            return filteredData.size();
        } catch (Exception e) {
            log.error("获取记录数量失败", e);
            return 0;
        }
    }



}
