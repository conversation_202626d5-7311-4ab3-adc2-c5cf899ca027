package com.wflow.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.AsyncTask;
import com.wflow.bean.entity.SysXzq14;
import com.wflow.bean.enums.TaskStatus;
import com.wflow.bean.vo.AsyncTaskVo;
import com.wflow.bean.vo.LandAcquisitionStatisticsVo;
import com.wflow.mapper.AsyncTaskMapper;
import com.wflow.mapper.SysXzq14Mapper;
import com.wflow.service.AsyncTaskService;
import com.wflow.service.CacheService;
import com.wflow.service.FormStatisticsService;
import com.wflow.utils.TaskResultSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 异步任务管理Service实现
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@Service
public class AsyncTaskServiceImpl implements AsyncTaskService {

    @Autowired
    private AsyncTaskMapper asyncTaskMapper;

    @Autowired
    private FormStatisticsService formStatisticsService;

    @Autowired
    private SysXzq14Mapper sysXzq14Mapper;

    @Autowired
    private CacheService cacheService;

    private static final String TASK_CACHE_PREFIX = "async_task:";
    private static final String TASK_RESULT_PREFIX = "task_result:";
    private static final int DEFAULT_TASK_EXPIRE_HOURS = 24;
    private static final int DEFAULT_RESULT_EXPIRE_HOURS = 2;

    @Override
    public AsyncTask createTask(String taskType, String taskName, String parameters,
            Integer priority, Integer estimatedTime, String userId) {
        String taskId = IdUtil.simpleUUID();

        AsyncTask task = AsyncTask.builder()
                .taskId(taskId)
                .taskType(taskType)
                .taskName(taskName)
                .status(TaskStatus.PENDING)
                .progress(0)
                .priority(priority != null ? priority : 5)
                .parameters(parameters)
                .estimatedTime(estimatedTime)
                .createdBy(userId)
                .createdTime(LocalDateTime.now())
                .expireTime(LocalDateTime.now().plusHours(DEFAULT_TASK_EXPIRE_HOURS))
                .retryCount(0)
                .maxRetryCount(3)
                .build();

        asyncTaskMapper.insert(task);

        // 缓存任务信息
        cacheService.set(TASK_CACHE_PREFIX + taskId, task, DEFAULT_TASK_EXPIRE_HOURS, TimeUnit.HOURS);

        log.info("创建异步任务成功，任务ID: {}, 类型: {}", taskId, taskType);
        return task;
    }

    @Override
    public AsyncTaskVo submitTask(String taskType, String taskName, String parameters,
            Integer priority, Integer estimatedTime, String userId) {
        AsyncTask task = createTask(taskType, taskName, parameters, priority, estimatedTime, userId);

        return AsyncTaskVo.createSubmitResponse(task.getTaskId(), task.getStatus(), task.getEstimatedTime());
    }

    @Override
    public AsyncTaskVo getTaskStatus(String taskId) {
        AsyncTask task = getTaskById(taskId);
        if (task == null) {
            return null;
        }

        // 尝试从缓存获取结果
        Object result = null;
        if (task.getStatus() == TaskStatus.COMPLETED) {
            result = cacheService.get(TASK_RESULT_PREFIX + taskId, Object.class);
            if (result == null && StrUtil.isNotBlank(task.getResult())) {
                // 使用类型安全的反序列化工具
                result = TaskResultSerializer.deserialize(task.getResult());
            }
        }

        return AsyncTaskVo.createStatusResponse(
                task.getTaskId(),
                task.getStatus(),
                task.getProgress(),
                result,
                task.getErrorMessage(),
                task.getStartTime(),
                task.getEndTime());
    }

    @Override
    public AsyncTask getTaskById(String taskId) {
        // 先从缓存获取
        AsyncTask task = cacheService.get(TASK_CACHE_PREFIX + taskId, AsyncTask.class);
        if (task != null) {
            return task;
        }

        // 从数据库获取
        task = asyncTaskMapper.selectById(taskId);
        if (task != null) {
            // 更新缓存
            cacheService.set(TASK_CACHE_PREFIX + taskId, task, DEFAULT_TASK_EXPIRE_HOURS, TimeUnit.HOURS);
        }

        return task;
    }

    @Override
    public boolean updateTaskStatus(String taskId, TaskStatus status, Integer progress, String errorMessage) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        Integer actualTime = null;

        AsyncTask task = getTaskById(taskId);
        if (task == null) {
            return false;
        }

        if (status == TaskStatus.PROCESSING && task.getStartTime() == null) {
            startTime = now;
        }

        if (status.isTerminal()) {
            endTime = now;
            if (task.getStartTime() != null) {
                actualTime = (int) java.time.Duration.between(task.getStartTime(), now).getSeconds();
            }
        }

        int result = asyncTaskMapper.updateTaskStatus(taskId, status, progress, startTime, endTime, errorMessage,
                actualTime);

        if (result > 0) {
            // 更新缓存中的任务状态
            task.setStatus(status);
            task.setProgress(progress);
            task.setErrorMessage(errorMessage);
            if (startTime != null)
                task.setStartTime(startTime);
            if (endTime != null)
                task.setEndTime(endTime);
            if (actualTime != null)
                task.setActualTime(actualTime);

            cacheService.set(TASK_CACHE_PREFIX + taskId, task, DEFAULT_TASK_EXPIRE_HOURS, TimeUnit.HOURS);

            log.info("更新任务状态成功，任务ID: {}, 状态: {}, 进度: {}%", taskId, status, progress);
        }

        return result > 0;
    }

    @Override
    public boolean updateTaskProgress(String taskId, Integer progress) {
        int result = asyncTaskMapper.updateTaskProgress(taskId, progress);

        if (result > 0) {
            // 更新缓存
            AsyncTask task = getTaskById(taskId);
            if (task != null) {
                task.setProgress(progress);
                cacheService.set(TASK_CACHE_PREFIX + taskId, task, DEFAULT_TASK_EXPIRE_HOURS, TimeUnit.HOURS);
            }

            log.debug("更新任务进度成功，任务ID: {}, 进度: {}%", taskId, progress);
        }

        return result > 0;
    }

    @Override
    public boolean completeTask(String taskId, Object result) {
        try {
            // 使用类型安全的序列化工具
            String resultJson = TaskResultSerializer.serialize(result);
            LocalDateTime endTime = LocalDateTime.now();

            AsyncTask task = getTaskById(taskId);
            if (task == null) {
                return false;
            }

            Integer actualTime = null;
            if (task.getStartTime() != null) {
                actualTime = (int) java.time.Duration.between(task.getStartTime(), endTime).getSeconds();
            }

            int updateResult = asyncTaskMapper.updateTaskResult(taskId, resultJson, TaskStatus.COMPLETED, endTime,
                    actualTime);

            if (updateResult > 0) {
                // 缓存结果（保存原始对象，避免类型丢失）
                cacheService.set(TASK_RESULT_PREFIX + taskId, result, DEFAULT_RESULT_EXPIRE_HOURS, TimeUnit.HOURS);

                // 更新任务缓存
                task.setStatus(TaskStatus.COMPLETED);
                task.setProgress(100);
                task.setResult(resultJson);
                task.setEndTime(endTime);
                task.setActualTime(actualTime);
                cacheService.set(TASK_CACHE_PREFIX + taskId, task, DEFAULT_TASK_EXPIRE_HOURS, TimeUnit.HOURS);

                log.info("任务完成，任务ID: {}, 耗时: {}秒", taskId, actualTime);
            }

            return updateResult > 0;
        } catch (Exception e) {
            log.error("完成任务失败，任务ID: {}", taskId, e);
            return failTask(taskId, "完成任务时发生异常: " + e.getMessage());
        }
    }

    @Override
    public boolean failTask(String taskId, String errorMessage) {
        return updateTaskStatus(taskId, TaskStatus.FAILED, null, errorMessage);
    }

    @Override
    public boolean cancelTask(String taskId) {
        return updateTaskStatus(taskId, TaskStatus.CANCELLED, null, "任务已取消");
    }

    @Override
    public List<AsyncTask> getPendingTasks(int limit) {
        return asyncTaskMapper.selectPendingTasks(limit);
    }

    @Override
    public List<AsyncTask> getUserTasks(String userId, int limit) {
        return asyncTaskMapper.selectUserTasks(userId, limit);
    }

    @Override
    public int cleanupExpiredTasks() {
        LocalDateTime expireTime = LocalDateTime.now().minusHours(DEFAULT_TASK_EXPIRE_HOURS);
        return asyncTaskMapper.deleteExpiredCompletedTasks(expireTime);
    }

    @Override
    public boolean retryTask(String taskId) {
        AsyncTask task = getTaskById(taskId);
        if (task == null || !task.canRetry()) {
            return false;
        }

        asyncTaskMapper.incrementRetryCount(taskId);
        return updateTaskStatus(taskId, TaskStatus.PENDING, 0, null);
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Page<LandAcquisitionStatisticsVo>> executeLandStatisticsTask(
            String taskId, String xzqCode, Map<String, Object> filters,
                    String formName, String configName, Integer pageNo, Integer pageSize,
            String sortField, String sortOrder) {

        log.info("开始执行征地统计异步任务，任务ID: {}, 行政区: {}", taskId, xzqCode);

        try {
            // 更新任务状态为处理中
            updateTaskStatus(taskId, TaskStatus.PROCESSING, 10, null);

            // 执行征地统计查询
            updateTaskProgress(taskId, 30);

            Page<LandAcquisitionStatisticsVo> result = formStatisticsService.getLandAcquisitionStatistics(
                    xzqCode, filters, formName, configName,
                    pageNo, pageSize, sortField, sortOrder);

            updateTaskProgress(taskId, 80);

            // 完成任务
            completeTask(taskId, result);

            log.info("征地统计异步任务执行完成，任务ID: {}, 结果记录数: {}", taskId, result.getTotal());

            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            log.error("征地统计异步任务执行失败，任务ID: {}", taskId, e);
            failTask(taskId, "任务执行失败: " + e.getMessage());
            return CompletableFuture.failedFuture(e);
        }
    }

    @Override
    public Integer estimateTaskTime(String taskType, String parameters) {
        if ("LAND_STATISTICS".equals(taskType)) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> params = JSON.parseObject(parameters, Map.class);
                String xzqCode = (String) params.get("xzqCode");

                // 根据行政区级别估算处理时间
                SysXzq14 xzq = sysXzq14Mapper.selectOne(
                        new LambdaQueryWrapper<SysXzq14>().eq(SysXzq14::getCode, xzqCode));

                if (xzq != null) {
                    Short level = xzq.getLevel();
                    if (level != null) {
                        switch (level) {
                            case 1: // 省级
                                return 60; // 60秒
                            case 2: // 市级
                                return 30; // 30秒
                            case 3: // 县级
                                return 15; // 15秒
                            default:
                                return 10; // 10秒
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("估算任务时间失败，使用默认值", e);
            }
        }

        return 30; // 默认30秒
    }

    @Override
    public boolean shouldUseAsync(String xzqCode, String taskType) {
        if (!"LAND_STATISTICS".equals(taskType)) {
            return false;
        }

        try {
            SysXzq14 xzq = sysXzq14Mapper.selectOne(
                    new LambdaQueryWrapper<SysXzq14>().eq(SysXzq14::getCode, xzqCode));

            if (xzq != null && xzq.getLevel() != null) {
                // 省级和市级使用异步处理
                return xzq.getLevel() <= 2;
            }
        } catch (Exception e) {
            log.warn("判断是否使用异步处理失败", e);
        }

        return false;
    }

    @Override
    public Object getTaskStatistics() {
        try {
            List<Object> statusCounts = asyncTaskMapper.countTasksByStatus();

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("statusCounts", statusCounts);
            statistics.put("timestamp", LocalDateTime.now());

            return statistics;
        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            return null;
        }
    }

    @Override
    public Page<LandAcquisitionStatisticsVo> getLandStatisticsTaskResult(String taskId) {
        try {
            AsyncTaskVo taskVo = getTaskStatus(taskId);
            if (taskVo == null) {
                log.warn("任务不存在，任务ID: {}", taskId);
                return new Page<>();
            }

            if (taskVo.getStatus() != TaskStatus.COMPLETED) {
                log.warn("任务未完成，无法获取结果，任务ID: {}, 状态: {}", taskId, taskVo.getStatus());
                return new Page<>();
            }

            Object result = taskVo.getResult();
            if (result == null) {
                log.warn("任务结果为空，任务ID: {}", taskId);
                return new Page<>();
            }

            // 使用专门的征地统计结果反序列化方法
            if (result instanceof String) {
                return TaskResultSerializer.deserializeLandStatisticsResult((String) result);
            } else if (TaskResultSerializer.isLandStatisticsResult(result)) {
                @SuppressWarnings("unchecked")
                Page<LandAcquisitionStatisticsVo> page = (Page<LandAcquisitionStatisticsVo>) result;
                return page;
            } else {
                log.warn("任务结果类型不匹配，任务ID: {}, 结果类型: {}", taskId, result.getClass().getName());
                return new Page<>();
            }

        } catch (Exception e) {
            log.error("获取征地统计任务结果失败，任务ID: {}", taskId, e);
            return new Page<>();
        }
    }

    @Override
    public Object waitForTaskCompletion(String taskId, int timeoutSeconds) {
        if (StrUtil.isBlank(taskId) || timeoutSeconds <= 0) {
            log.warn("无效的任务ID或超时时间，任务ID: {}, 超时时间: {}秒", taskId, timeoutSeconds);
            return null;
        }

        long startTime = System.currentTimeMillis();
        long timeoutMillis = timeoutSeconds * 1000L;
        int pollIntervalMillis = 1000; // 每秒轮询一次

        log.info("开始等待任务完成，任务ID: {}, 超时时间: {}秒", taskId, timeoutSeconds);

        try {
            while (System.currentTimeMillis() - startTime < timeoutMillis) {
                AsyncTaskVo taskVo = getTaskStatus(taskId);

                if (taskVo == null) {
                    log.warn("任务不存在，任务ID: {}", taskId);
                    return null;
                }

                TaskStatus status = taskVo.getStatus();
                log.debug("任务状态检查，任务ID: {}, 状态: {}, 进度: {}%", taskId, status, taskVo.getProgress());

                switch (status) {
                    case COMPLETED:
                        log.info("任务完成，任务ID: {}, 耗时: {}秒", taskId,
                                (System.currentTimeMillis() - startTime) / 1000);
                        return taskVo.getResult();

                    case FAILED:
                        log.warn("任务执行失败，任务ID: {}, 错误信息: {}", taskId, taskVo.getErrorMessage());
                        return null;

                    case CANCELLED:
                        log.warn("任务已取消，任务ID: {}", taskId);
                        return null;

                    case TIMEOUT:
                        log.warn("任务已超时，任务ID: {}", taskId);
                        return null;

                    case PENDING:
                    case PROCESSING:
                        // 任务仍在进行中，继续等待
                        break;

                    default:
                        log.warn("未知任务状态，任务ID: {}, 状态: {}", taskId, status);
                        return null;
                }

                // 等待一段时间后再次检查
                try {
                    Thread.sleep(pollIntervalMillis);
                } catch (InterruptedException e) {
                    log.warn("等待任务完成时被中断，任务ID: {}", taskId);
                    Thread.currentThread().interrupt();
                    return null;
                }
            }

            // 超时
            log.warn("等待任务完成超时，任务ID: {}, 超时时间: {}秒", taskId, timeoutSeconds);
            return null;

        } catch (Exception e) {
            log.error("等待任务完成时发生异常，任务ID: {}", taskId, e);
            return null;
        }
    }
}
