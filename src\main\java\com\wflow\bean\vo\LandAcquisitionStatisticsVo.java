package com.wflow.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 征地统计结果VO
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "征地统计结果")
public class LandAcquisitionStatisticsVo {

    @Schema(description = "行政区编码")
    private String xzqCode;

    @Schema(description = "行政区名称")
    private String xzqName;

    @Schema(description = "行政区级别")
    private Short xzqLevel;

    @Schema(description = "批准征地面积(亩)")
    private BigDecimal approvedLandArea;

    @Schema(description = "批准征地费用(万元)")
    private BigDecimal approvedLandCost;

    @Schema(description = "批准安置农业人口数(人)")
    private BigDecimal approvedAgriculturalPopulation;

    @Schema(description = "核定征地面积(亩)")
    private BigDecimal verifiedLandArea;

    @Schema(description = "核定征地总费用(万元)")
    private BigDecimal verifiedLandCost;

    @Schema(description = "核定安置农业人口数(人)")
    private BigDecimal verifiedAgriculturalPopulation;

    @Schema(description = "实际完成征地面积(亩)")
    private BigDecimal actualCompletedLandArea;

    @Schema(description = "实际完成征地费用(万元)")
    private BigDecimal actualCompletedLandCost;

    @Schema(description = "实际完成安置农业人口数(人)")
    private BigDecimal actualCompletedAgriculturalPopulation;

    @Schema(description = "统计记录数量")
    private Integer recordCount;

    @Schema(description = "是否有下级行政区")
    private Boolean hasSubRegions;
}
