package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;


/**
 *@Author：qinyi
 *@Date：2025-1-8  09:46
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "流程方案表")
public class WflowProcessPlan {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 分组 ID(子系统ID)
    */
    @Schema(description ="分组 ID")
    private Long groupId;

    /**
    * 平台应用配置id
    */
    @Schema(description ="平台应用配置id")
    private Long platformAppConfId;

    @Schema(description ="分组名称")
    private String groupName;

    private String operater;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}