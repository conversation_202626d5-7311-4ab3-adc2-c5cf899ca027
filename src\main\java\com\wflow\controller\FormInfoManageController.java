package com.wflow.controller;

import com.wflow.bean.entity.WflowFormDir;
import com.wflow.bean.entity.WflowFormInfo;
import com.wflow.bean.vo.NodeFormVo;
import com.wflow.service.FormInfoManageService;
import com.wflow.service.ModelGroupService;
import com.wflow.service.ModelHistorysFormIdService;
import com.wflow.utils.R;
import com.wflow.utils.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import liquibase.pro.packaged.S;
import liquibase.pro.packaged.W;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngines;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> qinyi
 * @date : 2024/11/21
 */
@RestController
@RequestMapping("wflow/forminfo")
@Tag(name = "材料管理-环节材料管理")
public class FormInfoManageController {

    @Autowired
    private FormInfoManageService formInfoManageService;

    @Autowired
    private ModelHistorysFormIdService modelHistorysFormIdService;

    @PostMapping("/addDir")
    @Operation(summary = "新增材料目录")
    public Object createForminfoDir(@RequestBody WflowFormDir dir) {
        formInfoManageService.createWflowFormDir(dir);
        return R.ok("新增材料目录成功");
    }

    @PostMapping("/editDir")
    @Operation(summary = "修改材料目录")
    public Object editForminfoDir(@RequestBody WflowFormDir dir) {
        formInfoManageService.editWflowFormDir(dir);
        return R.ok("修改材料目录成功");
    }

    @DeleteMapping("/delDir")
    @Operation(summary = "删除材料目录")
    public Object delForminfoDir(@RequestParam String id){
        formInfoManageService.delForminfoDir(id);
        return R.ok("删除材料目录成功");
    }

    @GetMapping("/dirTree")
    @Operation(summary = "材料目录树")
    public Object getForminfoDirTree(){
        return R.ok(formInfoManageService.getForminfoDirTree());
    }

    @PostMapping("/addFormInfo")
    @Operation(summary = "新增材料(表单)")
    public Object createForminfo(@RequestBody WflowFormInfo formInfo){
        formInfoManageService.createWflowForm(formInfo);
        return R.ok("新增材料成功");
    }

    @PostMapping("/editFormInfo")
    @Operation(summary = "修改材料(表单)")
    public Object editForminfo(@RequestBody WflowFormInfo formInfo){
        formInfoManageService.editWflowForm(formInfo);
        return R.ok("修改材料成功");
    }

    @DeleteMapping("/delFormInfo")
    @Operation(summary = "删除材料(表单)")
    public Object delForminfo(@RequestParam String id){
        formInfoManageService.delWflowForm(id);
        return R.ok("删除材料成功");
    }
    @GetMapping("/getForminfo")
    @Operation(summary = "查询表单内容")
    public Object getForminfo(String id){
        return R.ok(formInfoManageService.getForminfo(id));
    }

    @GetMapping("/dirFormInfoList")
    @Operation(summary = "目录下材料列表")
    public Object getForminfoList(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(required = false) String materialName,
                                   @RequestParam(required = true) String formDirId){
        return R.ok(formInfoManageService.getForminfoList(materialName,formDirId,page,pageSize));
    }


    /////////////////////////////////////////材料管理模块结束,以下是环节材料所需接口///////////////////////////////////////////////////////////

    @GetMapping("/processList")
    @Operation(summary = "流程节点")
    public Object processList(@RequestParam(required = true) String modelHistorysId){
        return R.ok(formInfoManageService.getNodeList(modelHistorysId));
    }


    @PostMapping("/relationFormDir")
    @Operation(summary = "流程关联材料目录")
    public Object relationFormDir(@RequestParam String modelHistorysId,@RequestParam String formDirIds){
        return R.ok(this.modelHistorysFormIdService.relationFormDir(modelHistorysId,formDirIds));
    }

    @GetMapping("/getFormInfoByRelationFormDir")
    @Operation(summary = "查询此流程关联的材料目录下的所有材料")
    public Object getFormInfoByRelationFormDir(@RequestParam String modelHistorysId){
        return R.ok(this.modelHistorysFormIdService.getFormInfoByRelationFormDir(modelHistorysId));
    }

    @PostMapping("/relationNodeFormInfo")
    @Operation(summary = "流程节点关联材料")
    public Object relationNodeFormInfo(@RequestBody List<NodeFormVo> nodeFormVoList){
        System.out.println(nodeFormVoList);
        return R.ok(this.modelHistorysFormIdService.relationNodeFormInfo(nodeFormVoList));
    }

    @DeleteMapping("removeRelationNodeFormInfo")
    @Operation(summary = "移除流程节点关联材料")
    public Object removeRelationNodeFormInfo(@RequestParam String nodeId,@RequestParam String formInfoIds){
        return R.ok(this.modelHistorysFormIdService.removeRelationNodeFormInfo(nodeId,formInfoIds));
    }

    @DeleteMapping("removeRelationFormDir")
    @Operation(summary = "移除流程关联材料目录")
    public Object removeRelationFormDir(@RequestParam String modelHistorysId,@RequestParam String formDirIds){
        return R.ok(this.modelHistorysFormIdService.removeRelationFormDir(modelHistorysId,formDirIds));
    }

    @GetMapping("nodeFormInfoList")
    @Operation(summary = "查询节点下现有材料列表")
    public Object nodeFormInfoList(@RequestParam String nodeId){
        return R.ok(this.modelHistorysFormIdService.getNodeFormInfoList(nodeId));
    }

    @PostMapping("copyFormInfoByNodeId")
    @Operation(summary = "复制节点材料")
    public Object copyFormInfoByNodeId(@RequestParam String formNodeId,@RequestParam String toNodeId,@RequestParam String toNodeName){
        return R.ok(this.modelHistorysFormIdService.copyFormInfoByNodeId(formNodeId,toNodeId,toNodeName));
    }

    @GetMapping("relationFormDir")
    @Operation(summary = "流程下拥有的目录列表")
    public Object relationFormDir(@RequestParam String modelHistorysId){
        return R.ok(this.modelHistorysFormIdService.getRelationFormDirList(modelHistorysId));
    }
}
