package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author：qinyi
 * @Date：2024-11-20 16:48
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "表单目录表")
public class WflowFormDir implements Serializable {
    private static final long serialVersionUID = 478762346895361748L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description ="目录名称")
    private String dirName;

    @Schema(description ="显示名称")
    private String showName;

    @Schema(description ="父id")
    private String parentId;

    @Schema(description ="显示顺序")
    private Integer showSort;

    @Schema(description ="创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description ="更新时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description ="是否展开")
    private Boolean isExpand;

    @TableField(exist = false)
    @Schema(description ="目录子节点")
    @Builder.Default
    private List<WflowFormDir> children = new ArrayList<>();

    @TableField(exist = false)
    private Integer level;
}
