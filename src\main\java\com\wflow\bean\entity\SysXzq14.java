package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
    * 山西省行政区代码表
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "sys_xzq14")
public class SysXzq14  implements Serializable {
    @Schema(description = "id")
    private Long id;
    @Schema(description = "行政区编码")
    private String code;
    @Schema(description = "行政区名称")
    private String name;
    /**
     * 城乡分类代码
     */
    @Schema(description = "城乡分类代码")
    private String ruralCode;

    /**
     * 级别
     */
    @Schema(description = "级别")
    private Short level;
    @Schema(description = "父级id")
    private Long pId;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Short sort;
}