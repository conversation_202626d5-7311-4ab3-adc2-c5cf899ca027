package com.wflow.bean.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wflow.utils.JsonArrayTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(autoResultMap = true)
@Schema(description = "流程模型历史表")
public class WflowModelHistorys implements Serializable {
    private static final long serialVersionUID = 478762346225421748L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String processDefId;

    private String deployId;

    private String formId;

    private String formName;

    private Integer version;

    private String settings;

    private String groupId;

    private String process;

    private String processConfig;

    private String remark;

    private Date created;

    private String formItems;

    private String formAbstracts;

    private String formConfig;

    private String logo;

    @Schema(description ="业务类型")
    private String businessType;

    @Schema(description ="机构层级")
    private String institution;

    @Schema(description ="业务事项代码")
    private String businessMattersCode;

    @Schema(description ="流程流水号")
    private String processSnum;

    @Schema(description ="业务代码")
    private String businessCode;

    @Schema(description ="流程关联的材料id数组")
    @TableField(typeHandler = JsonArrayTypeHandler.class)
    private List<WflowFormInfo> materials;
    @Schema(description = "模型类型")
    private String  modelType;
    @Schema(description = "表单规则")
    private String materialsRules;
}
