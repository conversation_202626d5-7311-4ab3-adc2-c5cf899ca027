package com.wflow.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.WflowFormStatisticsConfig;
import com.wflow.bean.vo.FormMenuVo;
import com.wflow.bean.vo.FormStatisticsVo;
import com.alibaba.fastjson2.JSON;
import com.wflow.bean.vo.AsyncTaskVo;
import com.wflow.service.AsyncTaskService;
import com.wflow.service.FormStatisticsService;
import com.wflow.utils.R;
import com.wflow.utils.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

import java.util.List;

/**
 * 表单统计控制器
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@RestController
@RequestMapping("wflow/form/statistics")
@Tag(name = "表单统计管理")
public class FormStatisticsController {

    @Autowired
    private FormStatisticsService formStatisticsService;

    @Autowired
    private AsyncTaskService asyncTaskService;

    /**
     * 获取默认的行政区编码
     * 优先使用当前登录用户所属机构的行政区编码，如果获取失败则使用全国代码
     *
     * @return 行政区编码
     */
    private String getDefaultXzqCode() {
        try {
            return UserUtil.getCurrentUserXzqCodeStatic();
        } catch (Exception e) {
            // 如果获取失败，返回全国代码
            return "000000000000";
        }
    }

    /**
     * 创建统计配置
     */
    @PostMapping("/config")
    @Operation(summary = "创建统计配置")
    public Object createStatisticsConfig(@RequestBody WflowFormStatisticsConfig config) {
        String configId = formStatisticsService.createStatisticsConfig(config);
        return R.ok("创建统计配置成功" + configId);
    }

    /**
     * 更新统计配置
     */
    @PutMapping("/config")
    @Operation(summary = "更新统计配置")
    public Object updateStatisticsConfig(@RequestBody WflowFormStatisticsConfig config) {
        boolean success = formStatisticsService.updateStatisticsConfig(config);
        return success ? R.ok("更新统计配置成功") : R.error("更新统计配置失败");
    }

    /**
     * 删除统计配置
     */
    @DeleteMapping("/config/{configId}")
    @Operation(summary = "删除统计配置")
    public Object deleteStatisticsConfig(@PathVariable String configId) {
        boolean success = formStatisticsService.deleteStatisticsConfig(configId);
        return success ? R.ok("删除统计配置成功") : R.error("删除统计配置失败");
    }

    /**
     * 启用/禁用统计配置
     */
    @PutMapping("/config/{configId}/toggle")
    @Operation(summary = "启用/禁用统计配置")
    public Object toggleStatisticsConfig(@PathVariable String configId,
            @RequestParam Integer status) {
        boolean success = formStatisticsService.toggleStatisticsConfig(configId, status);
        String action = status == 1 ? "启用" : "禁用";
        return success ? R.ok(action + "统计配置成功") : R.error(action + "统计配置失败");
    }

    /**
     * 分页查询统计配置
     */
    @GetMapping("/config/list")
    @Operation(summary = "分页查询统计配置")
    public Object getStatisticsConfigs(@RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam(required = false) String formId,
            @RequestParam(required = false) Integer status) {
        Page<WflowFormStatisticsConfig> page = formStatisticsService.getStatisticsConfigs(
                pageNo, pageSize, formId, status);
        return R.ok(page);
    }
    /*
     * 查询菜单配置
     */
    @GetMapping("/config/menu/{type}")
    @Operation(summary = "查询菜单配置")
    public Object getMenu(@PathVariable String type) {
        List<FormMenuVo> list = formStatisticsService.getMenu(type);
        return R.ok(list);
    }

    /**
     * 根据表单ID获取统计配置
     */
    @GetMapping("/config/form/{formId}")
    @Operation(summary = "根据表单ID获取统计配置")
    public Object getStatisticsConfigsByFormId(@PathVariable String formId) {
        List<WflowFormStatisticsConfig> configs = formStatisticsService.getStatisticsConfigsByFormId(formId);
        return R.ok(configs);
    }

    /**
     * 根据表单ID获取统计配置
     */
    @GetMapping("/config/{id}")
    @Operation(summary = "根据ID获取统计配置")
    public Object getStatisticsConfigsById(@PathVariable String id) {
        WflowFormStatisticsConfig configs = formStatisticsService.getStatisticsConfigsById(id);
        return R.ok(configs);
    }
    /**
     * 根据表单ID获取统计配置
     */
    @GetMapping("/config/type/{type}")
    @Operation(summary = "根据类型获取统计配置")
    public Object getStatisticsConfigsByType(@PathVariable String type) {
        List<WflowFormStatisticsConfig> configs = formStatisticsService.getStatisticsConfigsByType(type);
        return R.ok(configs);
    }

    /**
     * 执行实时统计
     */
//    @GetMapping("/execute/{configId}")
//    @Operation(summary = "执行实时统计")
//    public Object executeRealTimeStatistics(@PathVariable String configId,
//            @RequestParam(defaultValue = "false") boolean includeFinished) {
//        FormStatisticsVo result = formStatisticsService.executeRealTimeStatistics(configId, includeFinished);
//        return R.ok(result);
//    }

    /**
     * 批量执行统计
     */
//    @PostMapping("/execute/batch")
//    @Operation(summary = "批量执行统计")
//    public Object executeBatchStatistics(@RequestBody List<String> configIds,
//            @RequestParam(defaultValue = "false") boolean includeFinished) {
//        List<FormStatisticsVo> results = formStatisticsService.executeBatchStatistics(configIds, includeFinished);
//        return R.ok(results);
//    }

    /**
     * 根据表单ID执行所有相关统计
     */
    @GetMapping("/execute/form/{formId}")
    @Operation(summary = "根据表单ID执行所有相关统计")
    public Object executeStatisticsByFormId(@PathVariable String formId,
            @RequestParam(defaultValue = "false") boolean includeFinished) {
        List<FormStatisticsVo> results = formStatisticsService.executeStatisticsByFormId(formId, includeFinished);
        return R.ok(results);
    }

    /**
     * 获取表单字段的所有可能值
     */
    @GetMapping("/field/values")
    @Operation(summary = "获取表单字段的所有可能值")
    public Object getFieldDistinctValues(@RequestParam String formId,
            @RequestParam String fieldId,
            @RequestParam(defaultValue = "50") Integer limit) {
        List<String> values = formStatisticsService.getFieldDistinctValues(formId, fieldId, limit);
        return R.ok(values);
    }

    /**
     * 刷新统计缓存
     */
//    @PostMapping("/cache/refresh/{configId}")
//    @Operation(summary = "刷新统计缓存")
//    public Object refreshStatisticsCache(@PathVariable String configId) {
//        boolean success = formStatisticsService.refreshStatisticsCache(configId);
//        return success ? R.ok("刷新统计缓存成功") : R.error("刷新统计缓存失败");
//    }

    /**
     * 获取统计概览（仪表板用）
     */
//    @GetMapping("/overview")
//    @Operation(summary = "获取统计概览")
//    public Object getStatisticsOverview(@RequestParam(required = false) String formId,
//            @RequestParam(defaultValue = "false") boolean includeFinished) {
//        if (formId != null) {
//            List<FormStatisticsVo> results = formStatisticsService.executeStatisticsByFormId(formId, includeFinished);
//            return R.ok(results);
//        } else {
//            // 获取所有启用的配置并执行统计
//            Page<WflowFormStatisticsConfig> configs = formStatisticsService.getStatisticsConfigs(1, 100, null, 1);
//            List<String> configIds = configs.getRecords().stream()
//                    .map(WflowFormStatisticsConfig::getId)
//                    .toList();
//            List<FormStatisticsVo> results = formStatisticsService.executeBatchStatistics(configIds, includeFinished);
//            return R.ok(results);
//        }
//    }
    @GetMapping("/home")
    @Operation(summary = "获取首页统计")
    public Object getstatistics(@RequestParam String type){
        return R.ok(formStatisticsService.getstatistics(type));
    }
    @GetMapping("/xzpStatistics")
    @Operation(summary = "根据行政区统计")
    public Object getXzpStatistics(@RequestParam(defaultValue = "140000000000") String xzqCode,@RequestParam(required = false) String fieldId,@RequestParam String formId) {
        return  R.ok(formStatisticsService.getXzpStatistics(xzqCode, fieldId,formId));
    }

    @GetMapping("/landAcquisitionStatistics")
    @Operation(summary = "征地统计")
    @Parameters({
            @Parameter(name = "xzqCode", description = "行政区代码，如果不指定则自动使用当前登录用户所属机构的行政区编码", required = false),
            @Parameter(name = "filters", description = "过滤条件，支持多字段过滤。格式：filters[字段名]=值。例如：{\n" +
                    "  \"field2976601246206\": \"想出去玩\",\n" +
                    "  \"field6138601358008\": \"办公使用\"\n" +
                    "}", required = false),
            @Parameter(name = "formName", description = "表单名称", required = false),
            @Parameter(name = "configName", description = "配置名称", required = false),
            @Parameter(name = "pageNo", description = "页码", required = false),
            @Parameter(name = "pageSize", description = "每页大小", required = false),
            @Parameter(name = "sortField", description = "排序字段", required = false),
            @Parameter(name = "sortOrder", description = "排序方向", required = false),
            @Parameter(name = "timeout", description = "超时时间（秒），默认300秒", required = false)
    })
    public Object getLandAcquisitionStatistics(
            @RequestParam(required = false) String xzqCode,
            @RequestParam(required = false) Map<String, Object> filters,
            @RequestParam(required = false) String formName,
            @RequestParam(required = false) String configName,
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String sortField,
            @RequestParam(required = false) String sortOrder) {

        // 如果未指定行政区编码，则使用当前用户所属机构的行政区编码
        if (xzqCode == null || xzqCode.trim().isEmpty()) {
            xzqCode = getDefaultXzqCode();
        }

        // 判断是否需要使用异步处理
//        if (asyncTaskService.shouldUseAsync(xzqCode, "LAND_STATISTICS")) {
//            // 自动使用异步处理，使用默认超时时间300秒
//            return getLandAcquisitionStatisticsAsync(xzqCode, isCompleted, landStatusFieldId,
//                    formName, configName, pageNo, pageSize, sortField, sortOrder, 300);
//        }

        // 使用同步处理
        return R.ok(formStatisticsService.getLandAcquisitionStatistics(xzqCode, filters,
                formName, configName, pageNo, pageSize, sortField, sortOrder));
    }


//    @GetMapping("/landAcquisitionStatistics/async")
//    @Operation(summary = "征地统计查询（异步）", description = "异步查询征地统计数据，等待处理完成后直接返回统计结果。适用于大数据量查询，后端使用异步处理提升性能，但接口会等待完成后返回完整结果。")
//    @Parameters({
//            @Parameter(name = "xzqCode", description = "行政区代码，如果不指定则自动使用当前登录用户所属机构的行政区编码", required = false),
//            @Parameter(name = "filters", description = "过滤条件，支持多字段过滤。格式：filters[字段名]=值。例如：filters[isCompleted]=true&filters[landStatusFieldId]=123。支持的字段类型包括String、Boolean、Number等", required = false),
//            @Parameter(name = "formName", description = "表单名称", required = false),
//            @Parameter(name = "configName", description = "配置名称", required = false),
//            @Parameter(name = "pageNo", description = "页码", required = false),
//            @Parameter(name = "pageSize", description = "每页大小", required = false),
//            @Parameter(name = "sortField", description = "排序字段", required = false),
//            @Parameter(name = "sortOrder", description = "排序方向", required = false),
//            @Parameter(name = "timeout", description = "超时时间（秒），默认300秒", required = false)
//    })
//    public Object getLandAcquisitionStatisticsAsync(
//            @RequestParam(required = false) String xzqCode,
//            @RequestParam(required = false) Map<String, Object> filters,
//            @RequestParam(required = false) String formName,
//            @RequestParam(required = false) String configName,
//            @RequestParam(defaultValue = "1") Integer pageNo,
//            @RequestParam(defaultValue = "10") Integer pageSize,
//            @RequestParam(required = false) String sortField,
//            @RequestParam(required = false) String sortOrder,
//            @RequestParam(defaultValue = "300") Integer timeout) {
//
//        // 如果未指定行政区编码，则使用当前用户所属机构的行政区编码
//        if (xzqCode == null || xzqCode.trim().isEmpty()) {
//            xzqCode = getDefaultXzqCode();
//        }
//
//        try {
//            // 验证超时时间
//            if (timeout <= 0 || timeout > 600) {
//                return R.error("超时时间必须在1-600秒之间");
//            }
//
//            // 构建任务参数
//            Map<String, Object> parameters = new HashMap<>();
//            parameters.put("xzqCode", xzqCode);
//            parameters.put("filters", filters);
//            parameters.put("formName", formName);
//            parameters.put("configName", configName);
//            parameters.put("pageNo", pageNo);
//            parameters.put("pageSize", pageSize);
//            parameters.put("sortField", sortField);
//            parameters.put("sortOrder", sortOrder);
//
//            String parametersJson = JSON.toJSONString(parameters);
//
//            // 估算处理时间
//            Integer estimatedTime = asyncTaskService.estimateTaskTime("LAND_STATISTICS", parametersJson);
//
//            // 提交异步任务
//            AsyncTaskVo taskVo = asyncTaskService.submitTask(
//                    "LAND_STATISTICS",
//                    "征地统计查询-" + xzqCode,
//                    parametersJson,
//                    5,
//                    estimatedTime,
//                    "current_user");
//
//            // 异步执行任务
//            asyncTaskService.executeLandStatisticsTask(
//                    taskVo.getTaskId(), xzqCode, filters,
//                    formName, configName, pageNo, pageSize, sortField, sortOrder);
//
//            // 等待任务完成
//            Object result = asyncTaskService.waitForTaskCompletion(taskVo.getTaskId(), timeout);
//
//            if (result == null) {
//                // 检查任务状态以确定失败原因
//                AsyncTaskVo finalTaskStatus = asyncTaskService.getTaskStatus(taskVo.getTaskId());
//                if (finalTaskStatus != null) {
//                    switch (finalTaskStatus.getStatus()) {
//                        case FAILED:
//                            return R.error("征地统计查询失败: " + finalTaskStatus.getErrorMessage());
//                        case CANCELLED:
//                            return R.error("征地统计查询已取消");
//                        case TIMEOUT:
//                            return R.error("征地统计查询超时");
//                        default:
//                            return R.error("征地统计查询超时，请稍后重试或增加超时时间");
//                    }
//                } else {
//                    return R.error("征地统计查询失败，任务不存在");
//                }
//            }
//
//            // 返回结果，格式与同步接口保持一致
//            return R.ok(result);
//
//        } catch (Exception e) {
//            return R.error("征地统计查询失败: " + e.getMessage());
//        }
//    }

//    @PostMapping("/landAcquisitionStatistics/submit")
//    @Operation(summary = "提交征地统计异步任务", description = "提交征地统计异步任务，立即返回任务ID，适用于需要异步处理但不等待结果的场景")
//    @Parameters({
//            @Parameter(name = "xzqCode", description = "行政区代码，如果不指定则自动使用当前登录用户所属机构的行政区编码", required = false),
//            @Parameter(name = "filters", description = "过滤条件，支持多字段过滤。格式：filters[字段名]=值。例如：filters[isCompleted]=true&filters[landStatusFieldId]=123。支持的字段类型包括String、Boolean、Number等", required = false),
//            @Parameter(name = "formName", description = "表单名称", required = false),
//            @Parameter(name = "configName", description = "配置名称", required = false),
//            @Parameter(name = "pageNo", description = "页码", required = false),
//            @Parameter(name = "pageSize", description = "每页大小", required = false),
//            @Parameter(name = "sortField", description = "排序字段", required = false),
//            @Parameter(name = "sortOrder", description = "排序方向", required = false)
//    })
//    public Object submitLandAcquisitionStatisticsTask(
//            @RequestParam(required = false) String xzqCode,
//            @RequestParam(required = false) Map<String, Object> filters,
//            @RequestParam(required = false) String formName,
//            @RequestParam(required = false) String configName,
//            @RequestParam(defaultValue = "1") Integer pageNo,
//            @RequestParam(defaultValue = "10") Integer pageSize,
//            @RequestParam(required = false) String sortField,
//            @RequestParam(required = false) String sortOrder) {
//
//        // 如果未指定行政区编码，则使用当前用户所属机构的行政区编码
//        if (xzqCode == null || xzqCode.trim().isEmpty()) {
//            xzqCode = getDefaultXzqCode();
//        }
//
//        try {
//            // 构建任务参数
//            Map<String, Object> parameters = new HashMap<>();
//            parameters.put("xzqCode", xzqCode);
//            parameters.put("filters", filters);
//            parameters.put("formName", formName);
//            parameters.put("configName", configName);
//            parameters.put("pageNo", pageNo);
//            parameters.put("pageSize", pageSize);
//            parameters.put("sortField", sortField);
//            parameters.put("sortOrder", sortOrder);
//
//            String parametersJson = JSON.toJSONString(parameters);
//
//            // 估算处理时间
//            Integer estimatedTime = asyncTaskService.estimateTaskTime("LAND_STATISTICS", parametersJson);
//
//            // 提交异步任务
//            AsyncTaskVo taskVo = asyncTaskService.submitTask(
//                    "LAND_STATISTICS",
//                    "征地统计查询-" + xzqCode,
//                    parametersJson,
//                    5,
//                    estimatedTime,
//                    "current_user");
//
//            // 异步执行任务
//            asyncTaskService.executeLandStatisticsTask(
//                    taskVo.getTaskId(), xzqCode, filters,
//                    formName, configName, pageNo, pageSize, sortField, sortOrder);
//
//            // 返回任务信息
//            return R.ok(taskVo);
//
//        } catch (Exception e) {
//            return R.error("提交异步任务失败: " + e.getMessage());
//        }
//    }

    @DeleteMapping("/landAcquisitionStatistics/cache")
    @Operation(summary = "清除征地统计缓存")
    public Object clearLandAcquisitionStatisticsCache(
            @RequestParam(required = false) String xzqCode,
            @RequestParam(required = false) Map<String, Object> filters,
            @RequestParam(required = false) String formName,
            @RequestParam(required = false) String configName) {
        return R.ok(
                formStatisticsService.clearLandAcquisitionStatisticsCache(xzqCode, filters, formName, configName));
    }
//    @GetMapping("/tj")
//    @Operation(summary = "统计")
//    public Object tj(@RequestParam String xzqCode,@RequestParam(required = false) String id) {
//        return  R.ok(formStatisticsService.calculateFieldSum(xzqCode, id,true));
//    }
}
