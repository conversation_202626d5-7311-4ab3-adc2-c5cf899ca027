package com.wflow.service.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.compress.CompressUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wflow.bean.entity.FileInfo;
import com.wflow.bean.enums.FileTypeEnum;
import com.wflow.service.FileService;
import com.wflow.utils.FileWflowUtil;
import com.wflow.utils.R;
import com.wflow.utils.ShapeFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2023/7/26 10:45 周三
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {


    @Override
    public Object shpZip2GeoJson(MultipartFile file) throws Exception {

        String filename = file.getOriginalFilename();
        if (StrUtil.isBlank(filename)) {
            return R.error("文件名不正确");
        }
        String suffix = filename.substring(filename.lastIndexOf(".") + 1);
        if (!StrUtil.equalsIgnoreCase(suffix, "zip")) {
            return R.error("文件格式不正确");
        }

        StopWatch stopWatch = new StopWatch("shp压缩包解压转geojson");

        // 先上传
        stopWatch.start("上传文件");
        FileInfo fileInfo = FileWflowUtil.uploadFile(file, FileTypeEnum.PACKAGE);
        stopWatch.stop();

        // 解压路径
        stopWatch.start("解压文件");
        String extractor = fileInfo.getServerUrl().substring(0, fileInfo.getServerUrl().lastIndexOf("."));

        // 解压
        CompressUtil.createExtractor(CharsetUtil.CHARSET_UTF_8,
                suffix,
                new File(fileInfo.getServerUrl()))
                .extract(new File(extractor));
        stopWatch.stop();

        // 解析成geojson
        stopWatch.start("解析成geojson");
        JSONObject jsonObject = ShapeFileUtil.shpZipToGeoJson(new File(extractor));
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return R.ok(jsonObject);
    }
}
