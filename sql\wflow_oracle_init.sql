CREATE TABLE " C##WFLOW"."WFLOW_CC_TASKS"
(	"ID" NUMBER(20,0) NOT NULL ENABLE,
     "INSTANCE_ID" VARCHAR2(40) NOT NULL ENABLE,
     "USER_ID" VARCHAR2(40) NOT NULL ENABLE,
     "CODE" VARCHAR2(40) NOT NULL ENABLE,
     "NODE_ID" VARCHAR2(40) NOT NULL ENABLE,
     "NODE_NAME" VARCHAR2(50) NOT NULL ENABLE,
     "CREATE_TIME" DATE NOT NULL ENABLE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."ccUser" ON " C##WFLOW"."WFLOW_CC_TASKS" ("USER_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_CC_TASKS.INSTANCE_ID is '审批实例ID';
comment on column  C##WFLOW.WFLOW_CC_TASKS.USER_ID is '抄送用户';
comment on column  C##WFLOW.WFLOW_CC_TASKS.CODE is '模板编号';
comment on column  C##WFLOW.WFLOW_CC_TASKS.NODE_ID is '抄送节点ID';
comment on column  C##WFLOW.WFLOW_CC_TASKS.NODE_NAME is '抄送节点名称';
comment on column  C##WFLOW.WFLOW_CC_TASKS.CREATE_TIME is '抄送时间';

CREATE TABLE " C##WFLOW"."WFLOW_DEPARTMENTS"
(	"ID" NUMBER(20,0) NOT NULL ENABLE,
     "DEPT_NAME" VARCHAR2(255) NOT NULL ENABLE,
     "LEADER" VARCHAR2(255),
     "PARENT_ID" NUMBER(20,0),
     "CREATED" DATE,
     "UPDATED" DATE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."parentID" ON " C##WFLOW"."WFLOW_DEPARTMENTS" ("PARENT_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_DEPARTMENTS.ID is '部门ID';
comment on column  C##WFLOW.WFLOW_DEPARTMENTS.DEPT_NAME is '部门名';
comment on column  C##WFLOW.WFLOW_DEPARTMENTS.LEADER is '部门主管';
comment on column  C##WFLOW.WFLOW_DEPARTMENTS.PARENT_ID is '父部门ID';
comment on column  C##WFLOW.WFLOW_DEPARTMENTS.CREATED is '创建时间';
comment on column  C##WFLOW.WFLOW_DEPARTMENTS.UPDATED is '更新时间';

CREATE TABLE " C##WFLOW"."WFLOW_FORM_DATA"
(	"ID" VARCHAR2(40) NOT NULL ENABLE,
     "INSTANCE_ID" VARCHAR2(40) NOT NULL ENABLE,
     "VERSION" NUMBER(10,0),
     "CODE" VARCHAR2(40) NOT NULL ENABLE,
     "DEF_ID" VARCHAR2(40) NOT NULL ENABLE,
     "FIELD_ID" VARCHAR2(40) NOT NULL ENABLE,
     "FIELD_KEY" VARCHAR2(40),
     "FIELD_NAME" VARCHAR2(255) NOT NULL ENABLE,
     "FIELD_TYPE" VARCHAR2(20) NOT NULL ENABLE,
     "IS_JSON" NUMBER(1,0) NOT NULL ENABLE,
     "FIELD_VALUE" CLOB NOT NULL ENABLE,
     "CREATE_TIME" DATE,
     "UPDATE_TIME" DATE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"
 LOB ("FIELD_VALUE") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)) ;

CREATE INDEX " C##WFLOW"."fdDefId" ON " C##WFLOW"."WFLOW_FORM_DATA" ("DEF_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."fdInstanceid" ON " C##WFLOW"."WFLOW_FORM_DATA" ("INSTANCE_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."fmcode" ON " C##WFLOW"."WFLOW_FORM_DATA" ("CODE")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_FORM_DATA.ID is '主键';
comment on column  C##WFLOW.WFLOW_FORM_DATA.INSTANCE_ID is '流程实例ID';
comment on column  C##WFLOW.WFLOW_FORM_DATA.VERSION is '该流程版本';
comment on column  C##WFLOW.WFLOW_FORM_DATA.CODE is '流程编号';
comment on column  C##WFLOW.WFLOW_FORM_DATA.DEF_ID is '流程定义ID';
comment on column  C##WFLOW.WFLOW_FORM_DATA.FIELD_ID is '表单字段ID';
comment on column  C##WFLOW.WFLOW_FORM_DATA.FIELD_KEY is '表单字段KEY';
comment on column  C##WFLOW.WFLOW_FORM_DATA.FIELD_NAME is '表单字段名称';
comment on column  C##WFLOW.WFLOW_FORM_DATA.FIELD_TYPE is '表单字段组件类型';
comment on column  C##WFLOW.WFLOW_FORM_DATA.IS_JSON is '字段值是否为JSON';
comment on column  C##WFLOW.WFLOW_FORM_DATA.FIELD_VALUE is '表单字段值';
comment on column  C##WFLOW.WFLOW_FORM_DATA.CREATE_TIME is '创建时间';
comment on column  C##WFLOW.WFLOW_FORM_DATA.UPDATE_TIME is '更新时间';

CREATE TABLE " C##WFLOW"."WFLOW_FORM_RECORD"
(	"ID" VARCHAR2(40) NOT NULL ENABLE,
     "INSTANCE_ID" VARCHAR2(40) NOT NULL ENABLE,
     "FIELD_ID" VARCHAR2(40) NOT NULL ENABLE,
     "OLD_VALUE" CLOB,
     "NEW_VALUE" CLOB,
     "CREATE_TIME" DATE NOT NULL ENABLE,
     "UPDATE_BY" VARCHAR2(20) NOT NULL ENABLE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  TABLESPACE "USERS"
 LOB ("OLD_VALUE") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES )
 LOB ("NEW_VALUE") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES ) ;

CREATE INDEX " C##WFLOW"."formFieldId" ON " C##WFLOW"."WFLOW_FORM_RECORD" ("FIELD_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."formInstanceId" ON " C##WFLOW"."WFLOW_FORM_RECORD" ("INSTANCE_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_FORM_RECORD.ID is '主键';
comment on column  C##WFLOW.WFLOW_FORM_RECORD.INSTANCE_ID is '流程实例ID';
comment on column  C##WFLOW.WFLOW_FORM_RECORD.FIELD_ID is '字段ID';
comment on column  C##WFLOW.WFLOW_FORM_RECORD.OLD_VALUE is '旧的值';
comment on column  C##WFLOW.WFLOW_FORM_RECORD.NEW_VALUE is '新的值';
comment on column  C##WFLOW.WFLOW_FORM_RECORD.CREATE_TIME is '修改的时间';
comment on column  C##WFLOW.WFLOW_FORM_RECORD.UPDATE_BY is '修改人ID';

CREATE TABLE " C##WFLOW"."WFLOW_MODEL_GROUPS"
(	"GROUP_ID" NUMBER(20,0) NOT NULL ENABLE,
     "GROUP_NAME" VARCHAR2(50) NOT NULL ENABLE,
     "SORT" NUMBER(10,0) NOT NULL ENABLE,
     "UPDATED" DATE,
     PRIMARY KEY ("GROUP_ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_MODEL_GROUPS.GROUP_NAME is '分组名';
comment on column  C##WFLOW.WFLOW_MODEL_GROUPS.SORT is '排序';
CREATE TABLE " C##WFLOW"."WFLOW_MODEL_HISTORYS"
(	"ID" NUMBER(20,0) NOT NULL ENABLE,
     "PROCESS_DEF_ID" VARCHAR2(40),
     "FORM_ID" VARCHAR2(40) NOT NULL ENABLE,
     "FORM_NAME" VARCHAR2(40) NOT NULL ENABLE,
     "VERSION" NUMBER(10,0) NOT NULL ENABLE,
     "LOGO" CLOB NOT NULL ENABLE,
     "SETTINGS" CLOB NOT NULL ENABLE,
     "GROUP_ID" NUMBER(20,0) NOT NULL ENABLE,
     "FORM_ITEMS" CLOB NOT NULL ENABLE,
     "DEPLOY_ID" VARCHAR2(40),
     "FORM_CONFIG" CLOB,
     "PROCESS_CONFIG" CLOB,
     "FORM_ABSTRACTS" CLOB,
     "PROCESS" CLOB NOT NULL ENABLE,
     "REMARK" VARCHAR2(255),
     "CREATED" DATE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"
 LOB ("LOGO") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("SETTINGS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("FORM_ITEMS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("FORM_CONFIG") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("PROCESS_CONFIG") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("FORM_ABSTRACTS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("PROCESS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)) ;

CREATE INDEX " C##WFLOW"."wmhDefId" ON " C##WFLOW"."WFLOW_MODEL_HISTORYS" ("PROCESS_DEF_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_MODEL_HISTORYS.PROCESS_DEF_ID is '流程定义的ID';
comment on column  C##WFLOW.WFLOW_MODEL_HISTORYS.DEPLOY_ID is '部署后的ID';
comment on column  C##WFLOW.WFLOW_MODEL_HISTORYS.FORM_CONFIG is '表单全局设置';
comment on column  C##WFLOW.WFLOW_MODEL_HISTORYS.PROCESS_CONFIG is '流程附加设置项';
comment on column  C##WFLOW.WFLOW_MODEL_HISTORYS.FORM_ABSTRACTS is '表单摘要字段';

CREATE TABLE " C##WFLOW"."WFLOW_MODEL_PERMS"
(	"ID" VARCHAR2(40) NOT NULL ENABLE,
     "FORM_ID" VARCHAR2(40) NOT NULL ENABLE,
     "PERM_TYPE" VARCHAR2(5) NOT NULL ENABLE,
     "ORG_ID" VARCHAR2(40) NOT NULL ENABLE,
     "CREATE_TIME" DATE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."code" ON " C##WFLOW"."WFLOW_MODEL_PERMS" ("FORM_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."org" ON " C##WFLOW"."WFLOW_MODEL_PERMS" ("ORG_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_MODEL_PERMS.FORM_ID is '表单流程ID';
comment on column  C##WFLOW.WFLOW_MODEL_PERMS.PERM_TYPE is '权限类型 USER = 人、DEPT = 部门';
comment on column  C##WFLOW.WFLOW_MODEL_PERMS.ORG_ID is '部门ID或人员ID';

CREATE TABLE " C##WFLOW"."WFLOW_MODELS"
(	"FORM_ID" VARCHAR2(40) NOT NULL ENABLE,
     "PROCESS_DEF_ID" VARCHAR2(40),
     "DEPLOY_ID" VARCHAR2(40),
     "VERSION" NUMBER(10,0) NOT NULL ENABLE,
     "FORM_NAME" VARCHAR2(50) NOT NULL ENABLE,
     "LOGO" CLOB NOT NULL ENABLE,
     "SETTINGS" CLOB NOT NULL ENABLE,
     "GROUP_ID" NUMBER(20,0) NOT NULL ENABLE,
     "FORM_ITEMS" CLOB NOT NULL ENABLE,
     "FORM_CONFIG" CLOB,
     "PROCESS" CLOB NOT NULL ENABLE,
     "PROCESS_CONFIG" CLOB,
     "FORM_ABSTRACTS" CLOB,
     "REMARK" VARCHAR2(125),
     "SORT" NUMBER(10,0) NOT NULL ENABLE,
     "IS_DELETE" NUMBER(1,0) NOT NULL ENABLE,
     "IS_STOP" NUMBER(1,0) NOT NULL ENABLE,
     "CREATED" DATE NOT NULL ENABLE,
     "UPDATED" DATE NOT NULL ENABLE,
     PRIMARY KEY ("FORM_ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"
 LOB ("LOGO") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("SETTINGS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("FORM_ITEMS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("FORM_CONFIG") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("PROCESS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("PROCESS_CONFIG") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT))
 LOB ("FORM_ABSTRACTS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)) ;

CREATE INDEX " C##WFLOW"."defId" ON " C##WFLOW"."WFLOW_MODELS" ("PROCESS_DEF_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."deleted" ON " C##WFLOW"."WFLOW_MODELS" ("IS_DELETE")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_MODELS.FORM_ID is '表单ID';
comment on column  C##WFLOW.WFLOW_MODELS.PROCESS_DEF_ID is '流程定义ID';
comment on column  C##WFLOW.WFLOW_MODELS.DEPLOY_ID is '部署后的ID';
comment on column  C##WFLOW.WFLOW_MODELS.VERSION is '当前版本';
comment on column  C##WFLOW.WFLOW_MODELS.FORM_NAME is '表单名称';
comment on column  C##WFLOW.WFLOW_MODELS.LOGO is '图标配置';
comment on column  C##WFLOW.WFLOW_MODELS.SETTINGS is '设置项';
comment on column  C##WFLOW.WFLOW_MODELS.GROUP_ID is '分组ID';
comment on column  C##WFLOW.WFLOW_MODELS.FORM_ITEMS is '表单设置内容';
comment on column  C##WFLOW.WFLOW_MODELS.FORM_CONFIG is '表单全局设置';
comment on column  C##WFLOW.WFLOW_MODELS.PROCESS is '流程设置内容';
comment on column  C##WFLOW.WFLOW_MODELS.PROCESS_CONFIG is '流程附加设置项';
comment on column  C##WFLOW.WFLOW_MODELS.FORM_ABSTRACTS is '表单摘要字段';
comment on column  C##WFLOW.WFLOW_MODELS.REMARK is '备注';
comment on column  C##WFLOW.WFLOW_MODELS.IS_STOP is '0 正常 1=停用 2=已删除';
comment on column  C##WFLOW.WFLOW_MODELS.CREATED is '创建时间';
comment on column  C##WFLOW.WFLOW_MODELS.UPDATED is '更新时间';

CREATE TABLE " C##WFLOW"."WFLOW_NOTIFYS"
(	"ID" VARCHAR2(40) NOT NULL ENABLE,
     "TITLE" VARCHAR2(255) NOT NULL ENABLE,
     "NODE_ID" VARCHAR2(30),
     "USER_ID" VARCHAR2(50) NOT NULL ENABLE,
     "TYPE" VARCHAR2(10),
     "INSTANCE_ID" VARCHAR2(40),
     "CONTENT" VARCHAR2(255) NOT NULL ENABLE,
     "READED" NUMBER(1,0),
     "LINK" VARCHAR2(255),
     "CREATE_TIME" DATE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_NOTIFYS.TITLE is '标题';
comment on column  C##WFLOW.WFLOW_NOTIFYS.NODE_ID is '关联的流程节点ID';
comment on column  C##WFLOW.WFLOW_NOTIFYS.USER_ID is '用户ID';
comment on column  C##WFLOW.WFLOW_NOTIFYS.TYPE is '消息类型';
comment on column  C##WFLOW.WFLOW_NOTIFYS.INSTANCE_ID is '审批实例ID';
comment on column  C##WFLOW.WFLOW_NOTIFYS.CONTENT is '内容';
comment on column  C##WFLOW.WFLOW_NOTIFYS.READED is '是否已读';
comment on column  C##WFLOW.WFLOW_NOTIFYS.LINK is '跳转链接';

CREATE TABLE " C##WFLOW"."WFLOW_ROLES"
(	"ROLE_ID" VARCHAR2(20) NOT NULL ENABLE,
     "ROLE_NAME" VARCHAR2(50) NOT NULL ENABLE,
     "CREATED" DATE,
     PRIMARY KEY ("ROLE_ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_ROLES.ROLE_NAME is '角色ID';
comment on column  C##WFLOW.WFLOW_ROLES.CREATED is '创建时间';

CREATE TABLE " C##WFLOW"."WFLOW_SUB_GROUPS"
(	"GROUP_ID" NUMBER(20,0) NOT NULL ENABLE,
     "GROUP_NAME" VARCHAR2(255),
     "SORT" NUMBER(10,0),
     "CREATED" DATE,
     "UPDATED" DATE,
     PRIMARY KEY ("GROUP_ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_SUB_GROUPS.GROUP_ID is '分组ID';
comment on column  C##WFLOW.WFLOW_SUB_GROUPS.GROUP_NAME is '分组名';
comment on column  C##WFLOW.WFLOW_SUB_GROUPS.SORT is '排序号';
comment on column  C##WFLOW.WFLOW_SUB_GROUPS.CREATED is '创建时间';
comment on column  C##WFLOW.WFLOW_SUB_GROUPS.UPDATED is '更新时间';

CREATE TABLE " C##WFLOW"."WFLOW_SUB_PROCESS"
(	"ID" VARCHAR2(50) NOT NULL ENABLE,
     "PROC_CODE" VARCHAR2(255) NOT NULL ENABLE,
     "PROC_DEF_ID" VARCHAR2(50),
     "DEPLOY_ID" VARCHAR2(50),
     "PROCESS" CLOB,
     "PROC_NAME" VARCHAR2(255) NOT NULL ENABLE,
     "VERSION" NUMBER(3,0),
     "GROUP_ID" NUMBER(20,0),
     "SORT" NUMBER(3,0),
     "IS_STOP" NUMBER(1,0),
     "IS_DELETED" NUMBER(1,0),
     "REMARK" VARCHAR2(255),
     "CREATED" DATE,
     "UPDATED" DATE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  TABLESPACE "USERS"
 LOB ("PROCESS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES );

comment on column  C##WFLOW.WFLOW_SUB_PROCESS.ID is '子流程ID';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.PROC_CODE is '流程编号';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.PROC_DEF_ID is '子流程定义ID';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.PROCESS is '子流程设计JSON';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.PROC_NAME is '子流程名称';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.VERSION is '子流程版本号';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.GROUP_ID is '分组ID';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.SORT is '排序';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.IS_STOP is '是否已停用';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.IS_DELETED is '已删除';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.REMARK is '备注';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.CREATED is '创建时间';
comment on column  C##WFLOW.WFLOW_SUB_PROCESS.UPDATED is '更新时间';

CREATE TABLE " C##WFLOW"."WFLOW_USER_AGENTS"
(	"USER_ID" VARCHAR2(40) NOT NULL ENABLE,
     "AGENT_USER_ID" VARCHAR2(40) NOT NULL ENABLE,
     "START_TIME" DATE NOT NULL ENABLE,
     "END_TIME" DATE,
     "CREATE_TIME" DATE,
     PRIMARY KEY ("USER_ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_USER_AGENTS.USER_ID is '用户ID';
comment on column  C##WFLOW.WFLOW_USER_AGENTS.AGENT_USER_ID is '审批代理人ID';
comment on column  C##WFLOW.WFLOW_USER_AGENTS.START_TIME is '代理开始日期';
comment on column  C##WFLOW.WFLOW_USER_AGENTS.END_TIME is '代理结束日期';

CREATE TABLE " C##WFLOW"."WFLOW_USER_DEPARTMENTS"
(	"ID" VARCHAR2(40) NOT NULL ENABLE,
     "USER_ID" VARCHAR2(40) NOT NULL ENABLE,
     "DEPT_ID" VARCHAR2(20) NOT NULL ENABLE,
     "CREATED" DATE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."dept" ON " C##WFLOW"."WFLOW_USER_DEPARTMENTS" ("DEPT_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

CREATE INDEX " C##WFLOW"."user" ON " C##WFLOW"."WFLOW_USER_DEPARTMENTS" ("USER_ID")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS";

comment on column  C##WFLOW.WFLOW_USER_DEPARTMENTS.USER_ID is '用户ID';
comment on column  C##WFLOW.WFLOW_USER_DEPARTMENTS.DEPT_ID is '部门ID';

CREATE TABLE " C##WFLOW"."WFLOW_USER_ROLES"
(	"ID" NUMBER(10,0) NOT NULL ENABLE,
     "USER_ID" VARCHAR2(40),
     "ROLE_ID" VARCHAR2(20),
     "CREATED" DATE,
     PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
)
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

comment on column  C##WFLOW.WFLOW_USER_ROLES.USER_ID is '用户ID';
comment on column  C##WFLOW.WFLOW_USER_ROLES.ROLE_ID is '标签ID';

CREATE TABLE "C##WFLOW"."WFLOW_USERS"
(	"USER_ID" VARCHAR2(40) NOT NULL ENABLE,
     "USER_NAME" VARCHAR2(50) NOT NULL ENABLE,
     "PINGYIN" VARCHAR2(50),
     "PY" VARCHAR2(5),
     "ALISA" VARCHAR2(50),
     "AVATAR" VARCHAR2(1024),
     "SIGN" CLOB,
     "SEX" NUMBER(1,0),
     "ENTRY_DATE" DATE,
     "LEAVE_DATE" DATE,
     "CREATED" DATE,
     "UPDATED" DATE,
     PRIMARY KEY ("USER_ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "USERS"  ENABLE
) SEGMENT CREATION IMMEDIATE
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"
 LOB ("SIGN") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW 4000 CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES
  STORAGE(INITIAL 262144 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)) ;

CREATE INDEX "C##WFLOW"."name" ON "C##WFLOW"."WFLOW_USERS" ("USER_NAME")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "WFLOW_PRO" ;

CREATE INDEX "C##WFLOW"."pingyin" ON "C##WFLOW"."WFLOW_USERS" ("PINGYIN")
    PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "WFLOW_PRO";

comment on column  C##WFLOW.WFLOW_USERS.USER_ID is '用户ID';
comment on column  C##WFLOW.WFLOW_USERS.USER_NAME is '用户名';
comment on column  C##WFLOW.WFLOW_USERS.PINGYIN is '拼音  全拼';
comment on column  C##WFLOW.WFLOW_USERS.PY is '拼音, 首字母缩写';
comment on column  C##WFLOW.WFLOW_USERS.ALISA is '昵称';
comment on column  C##WFLOW.WFLOW_USERS.AVATAR is '头像BASE64';
comment on column  C##WFLOW.WFLOW_USERS.SIGN is '默认签字';
comment on column  C##WFLOW.WFLOW_USERS.SEX is '性别';
comment on column  C##WFLOW.WFLOW_USERS.ENTRY_DATE is '入职日期';
comment on column  C##WFLOW.WFLOW_USERS.LEAVE_DATE is '离职日期';
comment on column  C##WFLOW.WFLOW_USERS.CREATED is '创建时间';
comment on column  C##WFLOW.WFLOW_USERS.UPDATED is '更新时间';

INSERT INTO "WFLOW_DEPARTMENTS" VALUES (35453, '业务部', '3286432', 4319868, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_DEPARTMENTS" VALUES (231535, '生产管理部', NULL, 1486186, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_DEPARTMENTS" VALUES (264868, '行政人事部', NULL, 1486186, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_DEPARTMENTS" VALUES (689698, '客服部', '489564', 4319868, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_DEPARTMENTS" VALUES (1486186, 'xx科技有限公司', '381496', 0, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_DEPARTMENTS" VALUES (4319868, '销售服务部', '927438', 1486186, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_DEPARTMENTS" VALUES (6179678, '研发部', '6418616', 1486186, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO "WFLOW_MODEL_GROUPS" VALUES (104, 'pro后端流程引擎测试-请勿动', 1, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_MODEL_GROUPS" VALUES (0, '已停用', 9999, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_MODEL_GROUPS" VALUES (1, '其他', 9998, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO "WFLOW_ROLES" VALUES ('BOOS', '董事长', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_ROLES" VALUES ('HR', '人事', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_ROLES" VALUES ('WFLOW_APPROVAL_ADMIN', '审批管理员', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (1, '381496', '1486186', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (2, '489564', '689698', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (3, '568898', '4319868', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (4, '6418616', '6179678', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (5, '61769798', '231535', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (6, '327382', '6179678', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (7, '8902743', '689698', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (8, '927438', '4319868', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (9, '3286432', '35453', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (10, '3243678', '35453', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_DEPARTMENTS" VALUES (11, '489564', '264868', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_ROLES" VALUES (1, '381496', 'BOOS', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_ROLES" VALUES (2, '489564', 'HR', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USER_ROLES" VALUES (3, '6418616', 'WFLOW_APPROVAL_ADMIN', TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO "WFLOW_USERS" VALUES (327382, '李富贵', 'lifugui', 'lfg', NULL, 'https://dd-static.jd.com/ddimg/jfs/t1/188230/26/28979/10654/633026fdEf64e5e84/fc5c07ab3d5eac19.png', NULL, 1, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (381496, '旅人', 'lvren', 'lr', 'lr', 'https://pic.rmb.bdstatic.com/bjh/203726324a891b1946ba223209cb3fee.png', NULL, 1, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (489564, '李秋香', 'liqiuxiang', 'lqx', 'lqx', NULL, NULL, 0, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (568898, '王翠花', 'wangcuihua', 'wch', 'wch', 'https://dd-static.jd.com/ddimg/jfs/t1/204270/25/26917/8646/63302601E2794a142/5b75f81e6d0c4856.png', NULL, 1, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (927438, '隔壁老王', 'gebilaowang', 'gblw', NULL, 'https://dd-static.jd.com/ddimg/jfs/t1/21515/30/18678/11719/633025abEe734404d/c2950fef75e96028.png', NULL, 1, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (3243678, '狗剩', 'gousheng', 'gs', NULL, 'https://dd-static.jd.com/ddimg/jfs/t1/177987/31/29200/17909/63302676E5c00167f/13c59e53269e9f67.png', NULL, 1, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (3286432, '铁蛋', 'tiedan', 'td', NULL, 'https://dd-static.jd.com/ddimg/jfs/t1/203154/8/26845/14302/633026b7Ea9b381f7/7e7c5d96fcda0d39.png', NULL, 1, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (6418616, '张三', 'zhangsan', 'zs', 'zs', NULL, NULL, 1, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (8902743, '张秋梅', 'zhengqiumei', 'zqm', NULL, NULL, NULL, 0, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
INSERT INTO "WFLOW_USERS" VALUES (61769798, '李四', 'lisi', 'ls', 'ls', NULL, NULL, 1, NULL, NULL, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO "WFLOW_SUB_GROUPS"  VALUES (0, '已停用', 99999, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
UPDATE "WFLOW_SUB_GROUPS" SET "GROUP_ID" = 0 WHERE "GROUP_NAME" = '已停用';

INSERT INTO "WFLOW_SUB_GROUPS"  VALUES (0, '已停用', 99999, TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2020-09-16 13:30:37', 'YYYY-MM-DD HH24:MI:SS'));
UPDATE "WFLOW_SUB_GROUPS" SET "GROUP_ID" = 0 WHERE "GROUP_NAME" = '已停用';
