package com.wflow.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.wflow.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务实现类
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@Service
public class CacheServiceImpl implements CacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            log.debug("缓存设置成功，key: {}", key);
        } catch (Exception e) {
            log.error("缓存设置失败，key: {}", key, e);
        }
    }

    @Override
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            
            if (clazz.isInstance(value)) {
                return clazz.cast(value);
            }
            
            // 如果类型不匹配，尝试JSON转换
            String jsonStr = JSON.toJSONString(value);
            return JSON.parseObject(jsonStr, clazz);
        } catch (Exception e) {
            log.error("缓存获取失败，key: {}", key, e);
            return null;
        }
    }

    @Override
    public <T> List<T> getList(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            
            String jsonStr = JSON.toJSONString(value);
            return JSON.parseObject(jsonStr, new TypeReference<List<T>>() {});
        } catch (Exception e) {
            log.error("缓存列表获取失败，key: {}", key, e);
            return null;
        }
    }

    @Override
    public void delete(String key) {
        try {
            redisTemplate.delete(key);
            log.debug("缓存删除成功，key: {}", key);
        } catch (Exception e) {
            log.error("缓存删除失败，key: {}", key, e);
        }
    }

    @Override
    public boolean exists(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("缓存存在性检查失败，key: {}", key, e);
            return false;
        }
    }

    @Override
    public void expire(String key, long timeout, TimeUnit unit) {
        try {
            redisTemplate.expire(key, timeout, unit);
            log.debug("缓存过期时间设置成功，key: {}", key);
        } catch (Exception e) {
            log.error("缓存过期时间设置失败，key: {}", key, e);
        }
    }

    @Override
    public void deleteByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.debug("批量删除缓存成功，模式: {}，删除数量: {}", pattern, keys.size());
            }
        } catch (Exception e) {
            log.error("批量删除缓存失败，模式: {}", pattern, e);
        }
    }
}
