package com.wflow.workflow.bean.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 流程导出数据VO
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessExportVo {

    /**
     * 流程基本信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessBasicInfo {
        private String instanceId;
        private String processDefName;
        private String instanceName;
        private String processDefId;
        private String deployId;
        private Integer version;
        private String status;
        private String result;
        private String startUserId;
        private String startUserName;
        private String startUserDept;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String description;
        private String formData;
        private String formDir;
        private String progress;
    }

    /**
     * 表单数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormDataInfo {
        private String id;
        private String formItems;
        private String materialName;
        private String formConfig;
        private String formDirId;
    }

    /**
     * 附件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentInfo {
        private String fileId;
        private String fileName;
        private String fileType;
        private String filePath;
    }
    // 主要数据字段
    private ProcessBasicInfo processInfo;
    private List<FormDataInfo> formData;
    private List<AttachmentInfo> attachments;
    
    // 导出元数据
    private String exportUser;
    private String exportUserName;
    private LocalDateTime exportTime;
    private String exportReason;
}
