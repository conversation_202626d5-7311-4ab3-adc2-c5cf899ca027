package com.wflow.bean.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "流程模型")
public class WflowModelHistorysVo implements Serializable {
    private static final long serialVersionUID = 478762346225421748L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String processDefId;

    private String deployId;

    private String formId;

    private String formName;

    private Integer version;

    private String settings;

    private String groupId;

    private String process;

    private String processConfig;

    private String remark;

    private Date created;

    private String formItems;

    private String formAbstracts;

    private String formConfig;

    private String logo;

    @Schema(description ="流程关联的材料id数组")
    @TableField(exist = false)
    private List<String> materials;
    @Schema(description ="业务类型")
    private String businessType;

    @Schema(description ="机构层级")
    private String institution;

    @Schema(description ="业务事项代码")
    private String businessMattersCode;

    @Schema(description ="流程流水号")
    private String processSnum;

    @Schema(description ="业务代码")
    private String businessCode;
}
