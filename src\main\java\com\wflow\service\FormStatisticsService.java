package com.wflow.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.WflowFormStatisticsConfig;
import com.wflow.bean.vo.FormMenuVo;
import com.wflow.bean.vo.FormStatisticsVo;
import com.wflow.bean.vo.LandAcquisitionStatisticsVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 表单统计服务接口
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
public interface FormStatisticsService {

    /**
     * 创建统计配置
     * @param config 统计配置
     * @return 配置ID
     */
    String createStatisticsConfig(WflowFormStatisticsConfig config);

    /**
     * 更新统计配置
     * @param config 统计配置
     * @return 是否成功
     */
    boolean updateStatisticsConfig(WflowFormStatisticsConfig config);

    /**
     * 删除统计配置
     * @param configId 配置ID
     * @return 是否成功
     */
    boolean deleteStatisticsConfig(String configId);

    /**
     * 启用/禁用统计配置
     * @param configId 配置ID
     * @param status 状态：0=禁用，1=启用
     * @return 是否成功
     */
    boolean toggleStatisticsConfig(String configId, Integer status);

    /**
     * 分页查询统计配置
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param formId 表单ID（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    Page<WflowFormStatisticsConfig> getStatisticsConfigs(Integer pageNo, Integer pageSize, 
                                                          String formId, Integer status);

    /**
     * 根据表单ID获取统计配置
     * @param formId 表单ID
     * @return 统计配置列表
     */
    List<WflowFormStatisticsConfig> getStatisticsConfigsByFormId(String formId);

    /**
     * 执行实时统计
     * @param configId 统计配置ID
     * @param includeFinished 是否包含已完成的流程
     * @return 统计结果
     */
    FormStatisticsVo executeRealTimeStatistics(String configId, boolean includeFinished);

    /**
     * 批量执行统计
     * @param configIds 统计配置ID列表
     * @param includeFinished 是否包含已完成的流程
     * @return 统计结果列表
     */
    List<FormStatisticsVo> executeBatchStatistics(List<String> configIds, boolean includeFinished);

    /**
     * 根据表单ID执行所有相关统计
     * @param formId 表单ID
     * @param includeFinished 是否包含已完成的流程
     * @return 统计结果列表
     */
    List<FormStatisticsVo> executeStatisticsByFormId(String formId, boolean includeFinished);

    /**
     * 获取表单字段的所有可能值（用于配置统计时的参考）
     * @param formId 表单ID
     * @param fieldId 字段ID
     * @param limit 限制返回数量
     * @return 字段值列表
     */
    List<String> getFieldDistinctValues(String formId, String fieldId, Integer limit);

    /**
     * 刷新统计缓存
     * @param configId 统计配置ID
     * @return 是否成功
     */
    boolean refreshStatisticsCache(String configId);
    List<FormMenuVo> getMenu(String type);
    /**
     * 首页相关统计
     * @return 统计结果列表
     */
    Object getstatistics(String type);
    /**
     * 根据行政区相关统计
     * @return 统计结果列表
     */
    Object getXzpStatistics(String xzqCode, String fieldId, String formId);

    /**
     * 征地统计方法
     *
     * @param xzqCode    行政区编码
     * @param filters    过滤条件Map，支持多字段过滤。例如：{"isCompleted": true,
     *                   "landStatusFieldId": "123"}
     * @param formName   表单名称
     * @param configName 统计配置名称
     * @param pageNo     页码
     * @param pageSize   每页大小
     * @param sortField  排序字段
     * @param sortOrder  排序方向（ASC/DESC）
     * @return 分页的征地统计结果
     */
    Page<LandAcquisitionStatisticsVo> getLandAcquisitionStatistics(String xzqCode, Map<String, Object> filters,
                    String formName, String configName,
            Integer pageNo, Integer pageSize, String sortField, String sortOrder);

    /**
     * 清除征地统计缓存
     * 
     * @param xzqCode     行政区编码
     * @param formName    表单名称
     * @param configName  统计配置名称
     * @return 清除结果
     */
    String clearLandAcquisitionStatisticsCache(String xzqCode, Map<String, Object> filters, String formName,
                    String configName);

    /**
     * 计算指定表单字段的数值总和
     * 
     * @param formId          表单ID
     * @param fieldId         字段ID
     * @param includeFinished 是否包含已完成的流程实例
     * @return 字段数值总和
     */
    BigDecimal calculateFieldSum(String formId, String fieldId, boolean includeFinished);

    /**
     * 计算指定表单字段的数值总和（带机构权限过滤）
     *
     * @param formId          表单ID
     * @param fieldId         字段ID
     * @param includeFinished 是否包含已完成的流程实例
     * @param orgIds          机构ID列表，为空则不过滤
     * @return 字段数值总和
     */
    BigDecimal calculateFieldSumWithOrgFilter(String formId, String fieldId, boolean includeFinished,
            List<String> orgIds);

    /**
     * 计算字段求和（支持机构过滤和filters过滤）
     *
     * @param formId          表单ID
     * @param fieldId         字段ID
     * @param includeFinished 是否包含已完成的流程实例
     * @param orgIds          机构ID列表，为空则不过滤
     * @param filters         过滤条件Map，支持多字段过滤
     * @return 字段数值总和
     */
    BigDecimal calculateFieldSumWithOrgFilter(String formId, String fieldId, boolean includeFinished,
                    List<String> orgIds, Map<String, Object> filters);

    List<WflowFormStatisticsConfig> getStatisticsConfigsByType(String type);

    WflowFormStatisticsConfig getStatisticsConfigsById(String id);
}
