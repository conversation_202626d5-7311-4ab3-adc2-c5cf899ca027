package com.wflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wflow.bean.entity.WflowModelHistorys;
import com.wflow.bean.entity.WflowModelHistorysFormDir;
import com.wflow.bean.entity.WflowNodeFormInfo;
import com.wflow.bean.vo.ModelGroupVo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> qinyi
 * @date : 2024/12/16
 */
public interface WflowNodeFormInfoMapper extends BaseMapper<WflowNodeFormInfo> {

    @Select("SELECT * FROM wflow_models WHERE is_delete = false AND is_stop = false AND form_id IN (\n" +
            "\tSELECT form_id FROM wflow_model_perms WHERE perm_type = 'user' AND org_id = #{userId}\n" +
            "\tUNION All SELECT p.form_id FROM wflow_model_perms p, wflow_user_departments d \n" +
            "\tWHERE p.org_id = d.dept_id AND p.perm_type = 'dept' AND d.user_id = #{userId}\n" +
            ") OR form_id NOT IN (SELECT form_id FROM wflow_model_perms) ORDER by group_id ASC, sort ASC")
    WflowModelHistorys selectByPerms(@Param("id") String id);
    @Insert({
        "<script>",
        "INSERT INTO wflow_node_form_info (id, node_id, node_name, process_def_id, model_historys_id, form_info_id, created, version) ",
        "VALUES ",
        "<foreach collection='list' item='item' separator=','>",
        "(#{item.id}, #{item.nodeId}, #{item.nodeName}, #{item.processDefId}, #{item.modelHistorysId}, #{item.formInfoId}, #{item.created}, #{item.version})",
        "</foreach>",
        "</script>"
    })
    int insertNodeFormInfos(@Param("list") List<WflowNodeFormInfo> wflowNodeFormInfos);
}

