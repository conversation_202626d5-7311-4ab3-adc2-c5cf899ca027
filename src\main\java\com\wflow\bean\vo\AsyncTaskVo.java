package com.wflow.bean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wflow.bean.enums.TaskStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 异步任务响应VO
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsyncTaskVo {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 任务进度（0-100）
     */
    private Integer progress;

    /**
     * 任务优先级
     */
    private Integer priority;

    /**
     * 任务结果
     */
    private Object result;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 预估处理时间（秒）
     */
    private Integer estimatedTime;

    /**
     * 实际处理时间（秒）
     */
    private Integer actualTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 开始处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建异步提交响应
     */
    public static AsyncTaskVo createSubmitResponse(String taskId, TaskStatus status, Integer estimatedTime) {
        return AsyncTaskVo.builder()
                .taskId(taskId)
                .status(status)
                .estimatedTime(estimatedTime)
                .progress(0)
                .createdTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建任务状态响应
     */
    public static AsyncTaskVo createStatusResponse(String taskId, TaskStatus status, Integer progress, 
                                                   Object result, String errorMessage, 
                                                   LocalDateTime startTime, LocalDateTime endTime) {
        return AsyncTaskVo.builder()
                .taskId(taskId)
                .status(status)
                .progress(progress)
                .result(result)
                .errorMessage(errorMessage)
                .startTime(startTime)
                .endTime(endTime)
                .build();
    }
}
