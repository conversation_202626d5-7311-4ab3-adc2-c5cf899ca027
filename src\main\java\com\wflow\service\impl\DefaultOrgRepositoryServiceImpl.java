package com.wflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wflow.bean.do_.DeptDo;
import com.wflow.bean.do_.RoleDo;
import com.wflow.bean.do_.UserDeptDo;
import com.wflow.bean.do_.UserDo;
import com.wflow.bean.entity.*;
import com.wflow.bean.vo.ModelGroupVo;
import com.wflow.bean.vo.OrgTreeVo;
import com.wflow.bean.vo.UserVo;
import com.wflow.mapper.*;
import com.wflow.service.OrgRepositoryService;
import com.wflow.workflow.bean.process.ProcessNode;
import com.wflow.workflow.bean.process.enums.ApprovalTypeEnum;
import com.wflow.workflow.bean.process.form.FormPerm;
import com.wflow.workflow.bean.process.props.ApprovalProps;
import com.wflow.workflow.bean.process.props.CcProps;
import com.wflow.workflow.bean.process.props.RootProps;
import com.wflow.workflow.bean.process.props.SubProcessProps;
import com.wflow.workflow.service.ProcessNodeCatchService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 默认wflow的实现，集成自有系统需要自行实现接口
 * <AUTHOR> willian fu
 * @date : 2022/11/29
 */
@Service
public class DefaultOrgRepositoryServiceImpl implements OrgRepositoryService {

    @Autowired
    private WflowUsersMapper usersMapper;

    @Autowired
    private WflowRolesMapper rolesMapper;

    @Autowired
    private WflowDepartmentsMapper departsMapper;

    @Autowired
    private WflowUserDepartmentsMapper userDepartmentsMapper;

    @Autowired
    private WflowUserRolesMapper userRolesMapper;

    @Autowired
    private WflowModelPermsMapper modelPermsMapper;

    @Autowired
    private  WflowIInstitutionMapper institutionMapper;

    @Resource
    private WflowModelHistorysMapper wflowModelHistorysMapper;

    @Resource
    private ProcessNodeCatchService nodeCatchService;

    @Resource
    private SysXzq14Mapper sysXzq14Mapper;

    @Override
    public List<ModelGroupVo.Form> getModelsByPerm(String userId) {
        return modelPermsMapper.selectByPerms(userId);
    }

    @Override
    public UserDo getUserById(String userId) {
        User user = usersMapper.selectById(Long.valueOf(userId));
        if (Objects.nonNull(user)){
            return new UserDo(){{
                setUserId(user.getId().toString());
                setUserName(user.getNickName());
                setAvatar(user.getAvatar());
            }};
        }
        return null;
    }

    @Override
    public List<OrgTreeVo> selectUsersByPy(String py) {
        return usersMapper.selectUsersByPy(py);
    }

    @Override
    public List<OrgTreeVo> selectUsersByDept(String deptId) {
        List<OrgTreeVo> orgTreeVos = usersMapper.selectUsersByDept(Long.valueOf(deptId));
        orgTreeVos.forEach(v -> v.setType("user"));
        return orgTreeVos;
    }

    @Override
    public List<UserDo> getUsersBatch(Collection<String> userIds) {
        Collection<Long> userIds2 = userIds.stream().map(Long::valueOf).collect(Collectors.toList());
        try {
            return usersMapper.selectBatchIds(userIds2).stream()
                    .map(u -> new UserDo(u.getId().toString(), u.getNickName(), u.getAvatar()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public List<DeptDo> getDeptBatch(Collection<String> deptIds) {
        Collection<Long> deptIds2 = deptIds.stream().map(Long::valueOf).collect(Collectors.toList());
        try {
            return departsMapper.selectBatchIds(deptIds2).stream()
                    .map(u -> new DeptDo(u.getId().toString(), u.getDeptName(), u.getLeader(), u.getParentDeptId().toString()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public Set<String> getUsersByDepts(Collection<String> deptIds) {
        if(deptIds.isEmpty()) {
            return Collections.emptySet();
        }
        Collection<Long> deptIds2 = deptIds.stream().map(Long::valueOf).collect(Collectors.toList());
        try {
//            return userDepartmentsMapper.selectList(
//                    new LambdaQueryWrapper<WflowUserDepartments>()
//                            .select(WflowUserDepartments::getUserId)
//                            .in(WflowUserDepartments::getDeptId, deptIds))
//                    .stream().map(WflowUserDepartments::getUserId)
//                    .collect(Collectors.toSet());
            return usersMapper.selectList(new LambdaQueryWrapper<User>()
                    .select(User::getId)
                    .in(User::getDeptId, deptIds2))
                    .stream().map(u -> u.getId().toString()).collect(Collectors.toSet());
        } catch (Exception e) {
            return Collections.emptySet();
        }
    }

    @Override
    public DeptDo getDeptById(String deptId) {
//        WflowDepartments departments = departsMapper.selectById(deptId);
//        if (Objects.nonNull(departments)){
//            DeptDo deptDo = new DeptDo();
//            BeanUtils.copyProperties(departments, deptDo);
//            return deptDo;
//        }
        SafetyDept dept = departsMapper.selectById(Long.valueOf(deptId));
        if (Objects.nonNull(dept)){
            DeptDo deptDo = new DeptDo();
            deptDo.setId(dept.getId().toString());
            deptDo.setDeptName(dept.getDeptName());
            deptDo.setLeader(dept.getLeader());
            deptDo.setParentId(dept.getParentDeptId().toString());
            return deptDo;
        }
        return null;
    }

    @Override
    public List<DeptDo> getSysAllDepts() {
        try {
            return departsMapper.selectList(null).stream()
                    .map(d -> new DeptDo(d.getId().toString(), d.getDeptName(), d.getLeader(), d.getParentDeptId().toString()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public List<UserDeptDo> getSysAllUserDepts() {
        try {
//            return userDepartmentsMapper.selectList(null).stream()
//                    .map(d -> new UserDeptDo(d.getUserId(), d.getDeptId()))
//                    .collect(Collectors.toList());
            return usersMapper.selectList(new LambdaQueryWrapper<User>().eq(User::getIsLock,0)).stream()
                    .map(d -> new UserDeptDo(d.getId().toString(), d.getDeptId().toString()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public List<OrgTreeVo> getSubDeptById(String id ,String type){
        LambdaQueryWrapper<SafetyDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SafetyDept::getId, SafetyDept::getDeptName);
        if("dept".equals(type)){
            queryWrapper.eq(SafetyDept::getParentDeptId, Long.valueOf(id));
        }else {
            queryWrapper.eq(SafetyDept::getParentInstitutionId, Long.valueOf(id));
        }

        return departsMapper.selectList(queryWrapper)
                .stream().map(v -> OrgTreeVo.builder()
                        .id(v.getId().toString())
                        .name(v.getDeptName())
                        .type("dept").build())
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getRecursiveSubDept(String parentId) {
        List<String> list = new ArrayList<>();
        loadSubDept(parentId, list);
        return list;
    }

    @Override
    public List<String> getRecursiveSubInstitution(String parentId) {
        List<String> list = new ArrayList<>();
        loadSubInstitution(parentId, list);
        return list;
    }

    /**
     * 递归加载所有子部门
     * @param parentId 父部门ID
     * @param subDepts 所有子部门缓存
     */
    private void loadSubDept(String parentId, List<String> subDepts){
        List<SafetyDept> departments = departsMapper.selectList(
                new LambdaQueryWrapper<SafetyDept>()
                .eq(SafetyDept::getParentDeptId, Long.valueOf(parentId)));
        subDepts.addAll(departments.stream().map(d -> d.getId().toString()).collect(Collectors.toList()));
        departments.forEach(d -> loadSubDept(d.getId().toString(), subDepts));
    }
    /**
     * 递归加载所有子机构和部门
     * @param parentId 父部门ID
     * @param subDepts 所有子部门缓存
     */
    private void loadSubInstitution(String parentId, List<String> subDepts){
        List<String> subInstitution = new ArrayList<>();
        List<SafetyDept> departments = departsMapper.selectList(
                new LambdaQueryWrapper<SafetyDept>()
                        .eq(SafetyDept::getParentDeptId, Long.valueOf(parentId)));
        List<SafetyInstitution> institutions = institutionMapper.selectList(new LambdaQueryWrapper<SafetyInstitution>()
                .eq(SafetyInstitution::getParentId, Long.valueOf(parentId)));
        subDepts.addAll(departments.stream().map(d -> d.getId().toString()).collect(Collectors.toList()));
        subDepts.addAll(institutions.stream().map(d -> d.getId().toString()).collect(Collectors.toList()));
        subInstitution.addAll(departments.stream().map(d -> d.getId().toString()).collect(Collectors.toList()));
        subInstitution.addAll(institutions.stream().map(d -> d.getId().toString()).collect(Collectors.toList()));
        subInstitution.forEach(d -> loadSubDept(d, subDepts));
    }

    @Override
    public List<RoleDo> getSysAllRoles() {
        try {
            return rolesMapper.selectList(null).stream()
                    .map(r -> new RoleDo(r.getId().toString(), r.getRoleName()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public List<RoleDo> getSafetyInstitution(String id) {
        try {
            return institutionMapper.selectList(Wrappers.<SafetyInstitution>lambdaQuery().eq(SafetyInstitution::getParentId,Long.valueOf(id))).stream()
                    .map(r -> new RoleDo(r.getId().toString(), r.getInstitutionName()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }
    @Override
    public List<OrgTreeVo> getChildrenInstitution(String id) {
        try {
            return institutionMapper.selectList(Wrappers.<SafetyInstitution>lambdaQuery().eq(SafetyInstitution::getParentId,Long.valueOf(id))).stream()
                    .map(r -> OrgTreeVo.builder()
                            .id(r.getId().toString())
                            .name(r.getInstitutionName())
                            .type("institution").build())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public Set<String> getUsersByRoles(List<String> roles) {
        List<Long> roles2 = roles.stream().map(Long::valueOf).collect(Collectors.toList());
        return userRolesMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>()
                        .select(SafetyUserRole::getUserId)
                        .in(SafetyUserRole::getRoleId, roles2)).stream()
                .map(r -> r.getUserId().toString()).collect(Collectors.toSet());
    }

    @Override
    public String getUserSign(String userId) {
        return usersMapper.selectById(Long.valueOf(userId)).getSign();
    }

    @Override
    public void updateUserSign(String userId, String signature) {
        usersMapper.updateById(User.builder().id(Long.valueOf(userId)).sign(signature).build());
    }

    @Override
    public UserVo getUserDetail(String userId) {
        User user = usersMapper.selectById(Long.valueOf(userId));
        List<DeptDo> depts = getDeptsByUser(userId);
        List<WflowRoles> roles = rolesMapper.getRolesByUser(userId);
        List<SafetyInstitution> institutions = institutionMapper.selectList(Wrappers.<SafetyInstitution>lambdaQuery().eq(SafetyInstitution::getId, user.getInstitutionId()));
        SysXzq14 sysXzq14 = sysXzq14Mapper.selectOne(Wrappers.<SysXzq14>lambdaQuery().eq(SysXzq14::getCode, institutions.get(0).getXzqCode()));
        return UserVo.builder()
                .userId(userId)
                .username(user.getNickName())
                .sex(true)
                .avatar(user.getAvatar())
                .entryDate(user.getRegisterTime())
                .leaveDate(null)
                .positions(Collections.emptyList())
                .districtCode(institutions.stream().map(SafetyInstitution::getXzqCode).toString())
                .district(sysXzq14.getName())
                .depts(depts.stream().map(DeptDo::getDeptName).collect(Collectors.toList()))
                .roles(roles.stream().map(WflowRoles::getRoleName).collect(Collectors.toList()))
                .orgs(institutions.stream().map(SafetyInstitution::getInstitutionName).collect(Collectors.toList()))
                .build();
    }

    @Override
    public List<UserVo> getBatchUserDetail(Set<String> userIds) {
        return userIds.stream().map(this::getUserDetail).collect(Collectors.toList());
    }

    @Override
    public List<DeptDo> getDeptsByUser(String userId) {
        return userDepartmentsMapper.getUserDepts(Long.valueOf(userId));
    }
    public String getDeptNameByDeptId(String deptId) {
        return departsMapper.selectById(Long.valueOf(deptId)).getDeptName();
    }
    /*
    *判断用户是否和发起人同机构
    * */
    public boolean getInstitution(List<Long> userId,String defId,String nodeId) {
        boolean flag = true;
        WflowModelHistorys wflowModelHistorys = wflowModelHistorysMapper.selectOne(Wrappers.<WflowModelHistorys>lambdaQuery()
                .eq(WflowModelHistorys::getProcessDefId,defId));
        ProcessNode<?> currentNode = nodeCatchService.reloadProcessByStr(wflowModelHistorys.getProcess()).get(nodeId);
        ApprovalTypeEnum assignedType = null;
        switch (currentNode.getType()) {
            case ROOT, SUBPROC, CC:
                break;
            case TASK:
            case APPROVAL:
                ApprovalProps approvalProps = (ApprovalProps) currentNode.getProps();
                assignedType = approvalProps.getAssignedType();
                break;
        }
        if(ApprovalTypeEnum.ROLE.equals(assignedType)){
            List<Long> institutionId =  usersMapper.selectList(Wrappers.<User>lambdaQuery()
                    .select(User::getInstitutionId)
                    .in(User::getId,userId)
            ).stream().map(User::getInstitutionId).distinct().toList();
            if(institutionId.size()>1){
                flag = false;
            }
        }
        return flag;
    }
}
