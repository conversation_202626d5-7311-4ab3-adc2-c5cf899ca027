package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_institution")
public class SafetyInstitution {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 机构名称
     */
    @TableField(value = "institution_name")
    private String institutionName;

    /**
     * 级别id
     */
    @TableField(value = "level")
    private String level;

    /**
     * 机构简称
     */
    @TableField(value = "institution_short_name")
    private String institutionShortName;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    private Short showSort;

    /**
     * 行政区域编码
     */
    @TableField(value = "xzq_code")
    private String xzqCode;


    /**
     * 传真
     */
    @TableField(value = "fax_num")
    private String faxNum;

    /**
     * 联系电话
     */
    @TableField(value = "phone_num")
    private String phoneNum;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private String updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    private String operater;

    @TableField(value = "parent_id")
    @Schema(description = "父级id")
    private Long parentId;
}