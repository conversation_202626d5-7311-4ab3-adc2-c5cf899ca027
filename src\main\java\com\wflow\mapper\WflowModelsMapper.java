package com.wflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wflow.bean.entity.WflowModels;
import com.wflow.bean.vo.ModelGroupVo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> willian fu
 * @date : 2022/6/27
 */
public interface WflowModelsMapper extends BaseMapper<WflowModels> {

    @Select("SELECT * FROM wflow_models WHERE is_delete = false ORDER BY group_id ASC, sort ASC")
    List<ModelGroupVo.Form> getSysModels();

    @Select("SELECT * FROM wflow_models WHERE process_def_id = #{processDefId} AND is_delete = false LIMIT 1")
    WflowModels selectByProcessDefId(String processDefId);
}
