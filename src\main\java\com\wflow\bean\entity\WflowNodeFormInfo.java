package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author：qinyi
 * @Date：2024-11-20 16:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "节点材料关系表")
public class WflowNodeFormInfo implements Serializable {

    private static final long serialVersionUID = 478762346225443839L;


    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    //node_id
    @Schema(description ="流程节点id")
    private String nodeId;

    //node_name
    @Schema(description ="节点名称")
    private String nodeName;

    //process_def_id
    @Schema(description ="流程定义的 ID")
    private String processDefId;

    //model_historys_id
    @Schema(description ="父表主键id")
    private String modelHistorysId;

    //form_info_id
    @Schema(description ="关联的材料id")
    private String formInfoId;

    //created
    @Schema(description ="创建时间")
    @TableField(value = "created", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime created;

    @Schema(description ="版本号")
    private Integer version;

    @Schema(description ="部署id")
    private String deployId;
}
