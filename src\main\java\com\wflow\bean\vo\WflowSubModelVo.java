package com.wflow.bean.vo;


import com.wflow.bean.entity.WflowFormInfo;
import com.wflow.bean.entity.WflowSubProcess;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR> willian fu
 * @date : 2023/12/3
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WflowSubModelVo {
    //流程编号
    private String formId;

    //流程定义ID
    private String processDefId;
    /**
     * 表单名称
     */
    private String formName;

    //流程分组ID
    private Long groupId;

    //备注
    private String remark;
    /**
     * 流程设置内容
     */
    private String process;

    @Schema(name = "流程关联的材料id数组")
    private List<WflowFormInfo> materials;

    public WflowSubProcess cover(){
        return WflowSubProcess.builder()
                .procCode(formId)
                .procDefId(processDefId)
                .groupId(groupId)
                .process(process)
                .procName(formName)
                .remark(remark)
                .build();
    }
}
