<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wflow.mapper.SysXzq14Mapper">
  <resultMap id="BaseResultMap" type="com.wflow.bean.entity.SysXzq14">
    <!--@mbg.generated-->
    <!--@Table sys_xzq14-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="rural_code" jdbcType="VARCHAR" property="ruralCode" />
    <result column="level" jdbcType="SMALLINT" property="level" />
    <result column="p_id" jdbcType="BIGINT" property="pId" />
    <result column="sort" jdbcType="SMALLINT" property="sort" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, code, "name", rural_code, "level", p_id, sort
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from sys_xzq14
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from sys_xzq14
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wflow.bean.entity.SysXzq14">
    <!--@mbg.generated-->
    insert into sys_xzq14 (id, code, "name", 
      rural_code, "level", p_id, 
      sort)
    values (#{id,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{ruralCode,jdbcType=VARCHAR}, #{level,jdbcType=SMALLINT}, #{pId,jdbcType=BIGINT}, 
      #{sort,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.wflow.bean.entity.SysXzq14">
    <!--@mbg.generated-->
    insert into sys_xzq14
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="ruralCode != null">
        rural_code,
      </if>
      <if test="level != null">
        "level",
      </if>
      <if test="pId != null">
        p_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="ruralCode != null">
        #{ruralCode,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=SMALLINT},
      </if>
      <if test="pId != null">
        #{pId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wflow.bean.entity.SysXzq14">
    <!--@mbg.generated-->
    update sys_xzq14
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="ruralCode != null">
        rural_code = #{ruralCode,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        "level" = #{level,jdbcType=SMALLINT},
      </if>
      <if test="pId != null">
        p_id = #{pId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=SMALLINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wflow.bean.entity.SysXzq14">
    <!--@mbg.generated-->
    update sys_xzq14
    set code = #{code,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      rural_code = #{ruralCode,jdbcType=VARCHAR},
      "level" = #{level,jdbcType=SMALLINT},
      p_id = #{pId,jdbcType=BIGINT},
      sort = #{sort,jdbcType=SMALLINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>