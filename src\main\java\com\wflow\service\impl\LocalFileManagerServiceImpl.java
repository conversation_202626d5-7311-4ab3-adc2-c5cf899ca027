package com.wflow.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.wflow.bean.dto.CoordinateListVo;
import com.wflow.bean.entity.FileInfo;
import com.wflow.bean.enums.FileTypeEnum;
import com.wflow.bean.vo.FileResourceVo;
import com.wflow.bean.vo.GeoJsonObjectDto;
import com.wflow.exception.BusinessException;
import com.wflow.service.FileManagerService;
import com.wflow.utils.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 本地实现的文件管理服务
 * <AUTHOR> willian fu
 * @date : 2022/9/7
 */
@Slf4j
@Service
public class LocalFileManagerServiceImpl implements FileManagerService {

    @Value("${wflow.file.max-size:20}")
    private Integer maxSize;

    //默认文件存储路径
    private static final String BASE_DIR_FILE = "resource/file";
    private static final String BASE_SIGN_FILE = "resource/sign";
    //操作系统信息
    private final static String OS = System.getProperty("os.name").toLowerCase();

    @Override
    public FileResourceVo uploadFile(MultipartFile file, Boolean isImg, Boolean isSign) throws IOException {
        if (file.getSize() / 1048576 > maxSize){
            throw new BusinessException("管理员限制了最大文件大小为" + maxSize + "MB");
        }
        String name = file.getOriginalFilename();
        String md5 = SecureUtil.md5(file.getInputStream()).substring(8, 24);
        String type = name.substring(name.lastIndexOf("."));
        String fullPath = getPathByOs(true, isSign ? BASE_SIGN_FILE : BASE_DIR_FILE, md5) + type;
        FileResourceVo resourceVo = FileResourceVo.builder().id(md5 + type).isImage(isImg)
                .url("/wflow/res/" + md5 + type).size(file.getSize()).name(name).build();
        if (!FileUtil.exist(fullPath)){
            File touch = FileUtil.touch(fullPath);
            file.transferTo(touch);
            log.info("上传文件[{} => {}]成功", name, md5);
        }else {
            log.info("上传文件[{} => {}]成功，文件已存在", name, md5);
        }
        return resourceVo;
    }

    @Override
    public void delFileById(String fileId, Boolean isSign) {
        String path = getPathByOs(true, isSign ? BASE_SIGN_FILE : BASE_DIR_FILE, fileId);
        if (FileUtil.exist(path)){
            FileUtil.del(path);
        }else {
            throw new BusinessException("文件不存在");
        }
        log.info("删除文件[{}]成功", fileId);
    }

    @Override
    public InputStreamResource getFileById(String fileId, String name, Boolean isSign) throws IOException {
        String path = getPathByOs(true, isSign ? BASE_SIGN_FILE : BASE_DIR_FILE, fileId);
        if (FileUtil.exist(path)){
            return new InputStreamResource(new FileInputStream(path));
        }else {
            throw new BusinessException("文件不存在");
        }
    }

    @Override
    public Object shpZip2GeoJson(MultipartFile file) {

        return null;
    }

    @Override
    public Object txt2GeoJsonByGuo(MultipartFile file) throws IOException {
        // 获取后缀名
        String fileName = file.getOriginalFilename();
        String suffixName = fileName.substring(fileName.lastIndexOf("."));
        if (!StrUtil.equals(suffixName, ".txt")) {
            return R.error("文件格式错误");
        }

        // 文件上传
        FileInfo fileInfo = FileWflowUtil.uploadFile(file, FileTypeEnum.TXT);

        return Txt2GeoJsonUtil.txt2GeoJson(fileInfo.getServerUrl(), "");
    }

    @Override
    public Object shp2GeoJson(MultipartFile file) throws IOException {
        // 文件上传
        FileInfo fileInfo = FileWflowUtil.uploadFile(file, FileTypeEnum.FILE);
        // 解析成geojson
        return ShapeFileUtil.shpToGeoJson(fileInfo.getServerUrl());
    }

    @Override
    public Object excel2GeoJson(MultipartFile file) throws IOException {
        // 文件上传
        FileInfo fileInfo = FileWflowUtil.uploadFile(file, FileTypeEnum.EXCEL);

        return ExcelGeoJsonUtil.getGeoJson(fileInfo.getServerUrl(),"");
    }

    @Override
    public ResponseEntity<InputStreamResource> exportExcel(GeoJsonObjectDto geoJsonObjectDto, HttpServletRequest request, HttpServletResponse response) {
        // 完全绕过Spring包装
        try {
            Workbook workbook = ExcelExportUtil.exportExcel(
                    new ExportParams(null, "BL_PNT_COORD_HJ"),
                    CoordinateListVo.class,
                    geoJsonObjectDto.getCoordinateList()
            );
            String filePath = FileWflowUtil.getFILE_UPLOAD()+FileTypeEnum.EXCEL+"/坐标数据.xls";
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            } finally {
                workbook.close();
            }
            // 3. 创建输入流资源（确保正确关闭）
            InputStreamResource resource = new InputStreamResource(new FileInputStream(filePath));

            // 4. 设置响应头
            String fileName = "坐标数据.xls";
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            headers.setContentType(MediaType.parseMediaType("application/vnd.ms-excel"));
            // 5. 返回响应
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(filePath.length())
                    .body(resource);
//            File excelFile = new File("test.xls");
//            return FileWflowUtil.downloadFile(String.valueOf(excelFile));
//            try (FileOutputStream fos = new FileOutputStream(excelFile)) {
//                Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "BL_PNT_COORD_HJ"), CoordinateListVo.class, geoJsonObjectDto.getCoordinateList());
//                workbook.write(fos);
//                workbook.close();
//            }
            // 生成Excel文件
//            byte[] excelBytes = generateExcelFile(geoJsonObjectDto);
//            String base = Base64.getEncoder().encodeToString(excelBytes);
//            log.info("ssssss"+base+"ssssss");
//            response.reset();
//            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//            response.setCharacterEncoding("utf-8");
//            response.setHeader("Content-Disposition", "attachment; filename=test.xlsx");
//            response.setHeader("Pragma", "no-cache");
//            response.setHeader("Cache-Control", "no-cache");
//            response.setHeader("Expires", "0");
//            workbook.write(response.getOutputStream());
//            workbook.close();
//            try (OutputStream out = response.getOutputStream()) {
//                workbook.write(out);
//                out.flush();
            // 2. 直接传输文件
//            try (InputStream is = new FileInputStream(excelFile);
//                 OutputStream os = response.getOutputStream()) {
//
//                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//                response.setHeader("Content-Disposition", "attachment; filename=coordinates.xls");
//                response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
//                response.setHeader("Pragma", "no-cache");
//                response.setHeader("Expires", "0");
//                response.setContentLength((int) excelFile.length());
//                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition, download-filename");
//                response.setHeader("download-filename", "coordinates.xls");
//
//                byte[] buffer = new byte[4096];
//                int bytesRead;
//                while ((bytesRead = is.read(buffer)) != -1) {
//                    os.write(buffer, 0, bytesRead);
//                }
//                response.getOutputStream().flush();
//                os.flush();
//            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    public byte[] generateExcelFile(GeoJsonObjectDto geoJsonObjectDto) throws Exception{
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Sheet sheet = workbook.createSheet("BL_PNT_COORD_HJ");
            int rowNum = 0;
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            // 创建表头
            Row headerRow = sheet.createRow(rowNum++);
            String[] headers = { "界址点号", "纵坐标", "横坐标", "反算边长", "地块圈号", "备注"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            // 添加数据行
            if (CollectionUtil.isNotEmpty(geoJsonObjectDto.getCoordinateList())) {
                for (CoordinateListVo data : geoJsonObjectDto.getCoordinateList()) {
                    Row dataRow = sheet.createRow(rowNum++);
                    setCellValue(dataRow, 0, data.getName(), dataStyle);
                    setCellValue(dataRow, 1, data.getX(), dataStyle);
                    setCellValue(dataRow, 2, data.getY(), dataStyle);
                    setCellValue(dataRow, 3, data.getLength(), dataStyle);
                    setCellValue(dataRow, 4, data.getNumber(), dataStyle);
                    setCellValue(dataRow, 4, data.getRemark(), dataStyle);
                }
            }
            workbook.write(outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("生成Excel文件失败", e);
            throw new RuntimeException("生成Excel文件失败", e);
        }
    }

    @Override
    public ResponseEntity<StreamingResponseBody> exportTxt(GeoJsonObjectDto geoJsonObjectDto) {

        // 关键响应头设置
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        headers.set(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"coordinate.txt\"; filename*=coordinate.txt");
        headers.set(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        headers.set(HttpHeaders.PRAGMA, "no-cache");
        headers.set(HttpHeaders.EXPIRES, "0");
        StreamingResponseBody stream = outputStream -> {
            try (Writer writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8)) {
                writer.write(geoJsonObjectDto.getLandPointNumber()+","
                        +geoJsonObjectDto.getLandArea()+","
                        +geoJsonObjectDto.getLandNumber()+","
                        +geoJsonObjectDto.getLandName()+","
                        +geoJsonObjectDto.getLandType()+","+","+","+",@"
                );
                writer.write(System.lineSeparator());
                geoJsonObjectDto.getCoordinateList().forEach(coordinateListVo -> {
                            try {
                                writer.write(coordinateListVo.getName() + "," + coordinateListVo.getX() + "," + coordinateListVo.getY());
                                writer.write(System.lineSeparator());
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }
                );
                writer.flush();
            } catch (IOException e) {
                throw new RuntimeException("文件生成失败", e);
            }
        };
        // 构建响应实体
        return ResponseEntity.ok()
                .headers(headers)
                .body(stream);
    }
    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);

        return style;
    }
    /**
     * 设置单元格值
     */
    private void setCellValue(Row row, int columnIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(StrUtil.isNotBlank(value) ? value : "");
        cell.setCellStyle(cellStyle);
    }
    /**
     * @param isAb 是否相对路径
     * @param path xx.xx.xx
     * @return 对应系统下路径
     */
    public static synchronized String getPathByOs(boolean isAb, String ...path){
        return getString(isAb, OS, path);
    }

    public static String getString(boolean isAb, String os, String[] path) {
        StringBuilder builder = new StringBuilder(isAb ? System.getProperty("user.dir") : "");
        for (String pt : path) {
            builder.append("/").append(pt);
        }
        if (!isAb){
            builder.deleteCharAt(0);
        }
        return os.startsWith("win") ?
                builder.toString().replaceAll("/", "\\\\")
                : builder.toString();
    }
}
