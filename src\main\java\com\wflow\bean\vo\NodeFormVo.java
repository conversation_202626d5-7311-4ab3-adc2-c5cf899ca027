package com.wflow.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NodeFormVo {
    @Schema(description ="节点id")
    private String nodeId;
    @Schema(description ="节点名称")
    private String nodeName;
    @Schema(description ="流程id")
    private Long modelHistorysId;
    @Schema(description ="表单")
    private List<FormVo> form;
}
