package com.wflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wflow.bean.entity.*;
import com.wflow.bean.vo.NodeFormVo;
import com.wflow.bean.vo.WflowSubModelVo;
import com.wflow.mapper.*;
import com.wflow.service.ModelHistorysFormIdService;
import com.wflow.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> qinyi
 * @date : 202411/29
 */
@Slf4j
@Service
@Transactional
public class ModelHistorysFormIdServiceImpl implements ModelHistorysFormIdService {

    @Autowired
    private WflowModelHistorysFormIdMapper wflowModelHistorysFormIdMapper;

    @Autowired
    private WflowNodeFormInfoMapper wflowNodeFormInfoMapper;

    @Autowired
    private WflowFormInfoMapper wflowFormInfoMapper;

    @Autowired
    private WflowFormDirMapper wflowFormDirMapper;

    @Override
    public Object relationFormDir(String modelHistorysId, String formDirIds) {
        if(formDirIds.contains(",")){
            //formDirIds数组转list
            List<String> formDirIdList = Arrays.asList(formDirIds.split(","));
            formDirIdList.forEach(formDirId -> {
                WflowModelHistorysFormDir wflowModelHistorysFormDir = new WflowModelHistorysFormDir();
                wflowModelHistorysFormDir.setModelHistorysId(modelHistorysId);
                wflowModelHistorysFormDir.setFormDirId(formDirId);
                this.wflowModelHistorysFormIdMapper.insert(wflowModelHistorysFormDir);
            });
        }else{
            WflowModelHistorysFormDir wflowModelHistorysFormDir = new WflowModelHistorysFormDir();
            wflowModelHistorysFormDir.setModelHistorysId(modelHistorysId);
            wflowModelHistorysFormDir.setFormDirId(formDirIds);
            this.wflowModelHistorysFormIdMapper.insert(wflowModelHistorysFormDir);
        }
        return R.ok("关联材料目录成功!");
    }

    @Transactional
    @Override
    public Object relationNodeFormInfo(List<NodeFormVo> nodeFormVo) {
       nodeFormVo.forEach(map->{
            List<WflowNodeFormInfo> wflowNodeFormInfoList1 = map.getForm().stream().map(nodeFormVo1 -> {
                WflowNodeFormInfo wflowNodeFormInfo = new WflowNodeFormInfo();
                wflowNodeFormInfo.setNodeId(map.getNodeId());
                wflowNodeFormInfo.setNodeName(map.getNodeName());
                wflowNodeFormInfo.setFormInfoId(nodeFormVo1.getFormInfoIds());
                wflowNodeFormInfo.setCreated(LocalDateTime.now());
                wflowNodeFormInfo.setModelHistorysId(String.valueOf(map.getModelHistorysId()));
                wflowNodeFormInfo.setVersion(nodeFormVo1.getVersion());
                return wflowNodeFormInfo;
            }).collect(Collectors.toList());
           wflowNodeFormInfoMapper.delete(Wrappers.<WflowNodeFormInfo>lambdaQuery().eq(WflowNodeFormInfo::getModelHistorysId, map.getModelHistorysId()));
           wflowNodeFormInfoMapper.insertNodeFormInfos(wflowNodeFormInfoList1);
       });

        return R.ok("流程节点关联材料成功!");
    }

    @Override
    public Object removeRelationNodeFormInfo(String nodeId, String formInfoIds) {
        if(formInfoIds.contains(",")){
            //formInfoIds数组转list
            List<String> formInfoIdList = Arrays.asList(formInfoIds.split(","));
            LambdaQueryWrapper<WflowNodeFormInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WflowNodeFormInfo::getNodeId, nodeId);
            wrapper.in(WflowNodeFormInfo::getFormInfoId,formInfoIdList);
            this.wflowNodeFormInfoMapper.delete(wrapper);
        }else{
            LambdaQueryWrapper<WflowNodeFormInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WflowNodeFormInfo::getNodeId, nodeId);
            wrapper.eq(WflowNodeFormInfo::getFormInfoId,formInfoIds);
            this.wflowNodeFormInfoMapper.delete(wrapper);
        }
        return R.ok("移除流程节点关联材料成功!");
    }

    @Override
    @Transactional
    public Object removeRelationFormDir(String modelHistorysId, String formDirIds) {
        List<String> formDirIdList = Arrays.stream(formDirIds.split(","))
                .map(String::valueOf).collect(Collectors.toList());
        //先删除所属流程的节点下挂载的材料,再移除所选目录
        List<WflowFormInfo> wflowFormInfos = this.wflowFormInfoMapper.selectList(
                new LambdaQueryWrapper<WflowFormInfo>()
                        .in(WflowFormInfo::getFormDirId, formDirIdList));
        List<String> formInfoIds = wflowFormInfos.stream().map(WflowFormInfo::getId).collect(Collectors.toList());
        List<String> form = formInfoIds.stream()
                .map(Object::toString)
                .collect(Collectors.toList());
        this.wflowNodeFormInfoMapper.delete(
                new LambdaQueryWrapper<WflowNodeFormInfo>()
                        .eq(WflowNodeFormInfo::getModelHistorysId, modelHistorysId)
                        .in(WflowNodeFormInfo::getFormInfoId, form));
        this.wflowModelHistorysFormIdMapper.delete(
                new LambdaQueryWrapper<WflowModelHistorysFormDir>()
                        .eq(WflowModelHistorysFormDir::getModelHistorysId, modelHistorysId)
                        .in(WflowModelHistorysFormDir::getFormDirId, formDirIdList));
        return R.ok("移除流程关联材料目录成功!");
    }

    @Override
    public Object getNodeFormInfoList(String nodeId) {
        List<WflowNodeFormInfo> wflowNodeFormInfos = this.wflowNodeFormInfoMapper.selectList(new LambdaQueryWrapper<WflowNodeFormInfo>().eq(WflowNodeFormInfo::getNodeId, nodeId));
        if(wflowNodeFormInfos.isEmpty()){
            return R.ok("节点下无材料数据!");
        }
        List<String> formInfoIds = wflowNodeFormInfos.stream().map(WflowNodeFormInfo::getFormInfoId).collect(Collectors.toList());
        List<WflowFormInfo> wflowFormInfos = this.wflowFormInfoMapper.selectList(new LambdaQueryWrapper<WflowFormInfo>().in(WflowFormInfo::getId, formInfoIds));
        return R.ok(wflowFormInfos);
    }

    @Override
    @Transactional
    public Object copyFormInfoByNodeId(String formNodeId, String toNodeId, String toNodeName) {
        //查询需要被复制的节点材料
        List<WflowNodeFormInfo> wflowNodeFormInfos = this.wflowNodeFormInfoMapper.selectList(new LambdaQueryWrapper<WflowNodeFormInfo>().eq(WflowNodeFormInfo::getNodeId, formNodeId));
        //批量保存
        wflowNodeFormInfoMapper.insertNodeFormInfos(wflowNodeFormInfos);
        return R.ok("复制节点材料成功!");
    }

    @Override
    public Object getFormInfoByRelationFormDir(String modelHistorysId) {
        List<String> formDirIds = this.wflowModelHistorysFormIdMapper.selectList(new LambdaQueryWrapper<WflowModelHistorysFormDir>()
                .eq(WflowModelHistorysFormDir::getModelHistorysId, modelHistorysId))
                .stream().map(WflowModelHistorysFormDir::getFormDirId).collect(Collectors.toList());
        //查询此目录(递归查询子目录)下的所有材料
        List<String> selfAndChildFormDirIds = new ArrayList<>();
        formDirIds.forEach(formDirId -> {
            List<String> ids = this.wflowFormDirMapper.selectSelfAndChildIds(formDirId);
            selfAndChildFormDirIds.addAll(ids);
        });
        //根据目录list查询其下的所有材料
        List<WflowFormInfo> wflowFormInfos = this.wflowFormInfoMapper.selectList(new LambdaQueryWrapper<WflowFormInfo>()
                .in(WflowFormInfo::getFormDirId, selfAndChildFormDirIds));
        return R.ok(wflowFormInfos);
    }

    @Override
    public Object getRelationFormDirList(String modelHistorysId) {
        List<WflowModelHistorysFormDir> wflowModelHistorysFormDirs = this.wflowModelHistorysFormIdMapper.selectList(new LambdaQueryWrapper<WflowModelHistorysFormDir>()
                .eq(WflowModelHistorysFormDir::getModelHistorysId, modelHistorysId));
        if(!wflowModelHistorysFormDirs.isEmpty()){
            List<String> dirIds = wflowModelHistorysFormDirs.stream().map(WflowModelHistorysFormDir::getFormDirId).collect(Collectors.toList());
            List<WflowFormDir> wflowFormDirs = this.wflowFormDirMapper.selectList(new LambdaQueryWrapper<WflowFormDir>()
                    .in(WflowFormDir::getId, dirIds));
            return R.ok(wflowFormDirs);
        }else{
            return R.ok("此流程下无材料目录!");
        }
    }
    public Object insetForm(WflowModelHistorys models){
        List<WflowNodeFormInfo> wflowNodeFormInfoList1 = models.getMaterials().stream().map(map->{
            WflowNodeFormInfo wflowNodeFormInfo = new WflowNodeFormInfo();
            wflowNodeFormInfo.setModelHistorysId(models.getFormId());
            wflowNodeFormInfo.setFormInfoId(String.valueOf(map.getId()));
            wflowNodeFormInfo.setVersion(models.getVersion());
            return wflowNodeFormInfo;
        }).collect(Collectors.toList());
        wflowNodeFormInfoMapper.delete(Wrappers.<WflowNodeFormInfo>lambdaQuery()
                        .eq(WflowNodeFormInfo::getModelHistorysId, models.getFormId())
                        .eq(WflowNodeFormInfo::getVersion, models.getVersion()));
        wflowNodeFormInfoMapper.insertNodeFormInfos(wflowNodeFormInfoList1);
        return R.ok("保存成功!");
    }
    public Object insetSubForm(WflowSubModelVo models){
        List<WflowNodeFormInfo> wflowNodeFormInfoList1 = models.getMaterials().stream().map(map->{
            WflowNodeFormInfo wflowNodeFormInfo = new WflowNodeFormInfo();
            wflowNodeFormInfo.setModelHistorysId(models.getFormId());
            wflowNodeFormInfo.setFormInfoId(String.valueOf(map.getId()));
//            wflowNodeFormInfo.setVersion(models.getVersion());
            return wflowNodeFormInfo;
        }).collect(Collectors.toList());
        wflowNodeFormInfoMapper.delete(Wrappers.<WflowNodeFormInfo>lambdaQuery()
                .eq(WflowNodeFormInfo::getModelHistorysId, models.getFormId()));
//                .eq(WflowNodeFormInfo::getVersion, models.getVersion()));
        wflowNodeFormInfoMapper.insertNodeFormInfos(wflowNodeFormInfoList1);
        return R.ok("保存成功!");
    }
}
