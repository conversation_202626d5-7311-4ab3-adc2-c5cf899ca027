package com.wflow.bean.vo;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "表单字段查询菜单")
public class FormMenuVo {
    @Schema(description = "字段id")
    private String fieldId;
    @Schema(description = "字段名称")
    private String fieldName;
    @Schema(description = "配置类型")
    private String configName;
    @Schema(description = "字段设置内容")
    private JSONObject formItem ;
}
