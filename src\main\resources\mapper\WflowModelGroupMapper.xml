<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wflow.mapper.WflowModelGroupMapper">
  <resultMap id="BaseResultMap" type="com.wflow.bean.entity.WflowModelGroup">
    <!--@mbg.generated-->
    <!--@Table wflow_model_group-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, model_id, group_id, create_time
  </sql>

</mapper>