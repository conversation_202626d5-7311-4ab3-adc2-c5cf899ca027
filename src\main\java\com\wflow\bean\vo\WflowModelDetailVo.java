package com.wflow.bean.vo;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.wflow.bean.entity.WflowFormDir;
import com.wflow.bean.entity.WflowFormInfo;
import com.wflow.workflow.bean.process.ProcessNode;
import com.wflow.workflow.bean.process.form.Form;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> willian fu
 * @date : 2022/10/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WflowModelDetailVo {
    //流程ID
    private String formId;

    //流程定义ID
    private String processDefId;

    /**
     * 表单名称
     */
    private String formName;
    /**
     * 图标配置
     */
    private String logo;

    /**
     * 表单设置内容
     */
    private List<Form> formItems;

    //表单配置
    private JSONObject formConfig;
    /**
     * 流程设置内容
     */
    private ProcessNode<?> process;

    /*
    * 目录
    * */
    private  List<WflowFormDir> formDirs;

    @Schema(description ="流程关联的材料id数组")
    private List<WflowFormInfo> materials;
    @Schema(description = "流程表单规则")
    private JSONArray materialsRules;
}
